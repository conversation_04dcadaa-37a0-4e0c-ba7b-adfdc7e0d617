"""
测试所有修复
"""
import sys
import os
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def test_reply_interval_improvements():
    """测试回复间隔改进"""
    print("🔍 测试回复间隔改进...")
    
    try:
        from core.task_executor import TaskExecutor
        
        executor = TaskExecutor()
        
        # 测试智能间隔计算
        test_cases = [
            (30, 3, 0, "前5次回复"),
            (30, 8, 0, "6-10次回复"),
            (30, 15, 0, "11-20次回复"),
            (30, 25, 0, "20次以上回复"),
            (30, 10, 3, "有错误的情况")
        ]
        
        for base_interval, reply_count, error_count, description in test_cases:
            interval = executor._calculate_reply_interval(base_interval, reply_count, error_count)
            print(f"✅ {description}: 基础{base_interval}s → 实际{interval}s")
        
        # 测试智能等待方法
        if hasattr(executor, '_smart_sleep'):
            print("✅ 智能等待方法存在")
        else:
            print("❌ 智能等待方法不存在")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ 回复间隔改进测试失败: {e}")
        return False

def test_database_fixes():
    """测试数据库修复"""
    print("\n🔍 测试数据库修复...")
    
    try:
        from config.database import db
        
        # 测试新增的方法
        required_methods = [
            "update_keyword_stats",
            "get_task",
            "delete_task",
            "has_replied_to_note"
        ]
        
        missing_methods = []
        for method_name in required_methods:
            if not hasattr(db, method_name):
                missing_methods.append(method_name)
        
        if not missing_methods:
            print("✅ 数据库方法完整")
        else:
            print(f"❌ 缺少方法: {missing_methods}")
            return False
        
        # 测试update_keyword_stats方法
        try:
            db.update_keyword_stats("测试关键词", 1)
            print("✅ update_keyword_stats方法正常")
        except Exception as e:
            print(f"⚠️ update_keyword_stats方法异常: {e}")
        
        return True
        
    except Exception as e:
        print(f"❌ 数据库修复测试失败: {e}")
        return False

def test_main_window_fixes():
    """测试主窗口修复"""
    print("\n🔍 测试主窗口修复...")
    
    try:
        from gui.main_window import MainWindow
        
        # 检查refresh_task_list方法的安全性
        import inspect
        source = inspect.getsource(MainWindow.refresh_task_list)
        
        if "hasattr(self, 'task_tree')" in source:
            print("✅ refresh_task_list方法已添加安全检查")
        else:
            print("❌ refresh_task_list方法缺少安全检查")
            return False
        
        # 检查update_status方法的安全性
        source = inspect.getsource(MainWindow.update_status)
        
        if "hasattr(self, 'status_label')" in source:
            print("✅ update_status方法已添加安全检查")
        else:
            print("❌ update_status方法缺少安全检查")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ 主窗口修复测试失败: {e}")
        return False

def test_task_monitor_fixes():
    """测试任务监控修复"""
    print("\n🔍 测试任务监控修复...")
    
    try:
        from gui.task_monitor import TaskMonitorWindow
        
        # 检查create_performance_metrics方法
        import inspect
        source = inspect.getsource(TaskMonitorWindow.create_performance_metrics)
        
        if "grid_container" in source:
            print("✅ 任务监控布局管理器冲突已修复")
        else:
            print("❌ 任务监控布局管理器冲突未修复")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ 任务监控修复测试失败: {e}")
        return False

def test_error_handling():
    """测试错误处理"""
    print("\n🔍 测试错误处理...")
    
    try:
        from core.task_executor import TaskExecutor
        
        executor = TaskExecutor()
        
        # 测试错误情况下的间隔计算
        error_interval = executor._calculate_reply_interval(30, 5, 5)  # 有5个错误
        normal_interval = executor._calculate_reply_interval(30, 5, 0)  # 无错误
        
        if error_interval > normal_interval:
            print(f"✅ 错误时间隔增加: {normal_interval}s → {error_interval}s")
        else:
            print(f"⚠️ 错误时间隔未增加: {normal_interval}s → {error_interval}s")
        
        # 测试最小间隔限制
        min_interval = executor._calculate_reply_interval(5, 1, 0)  # 基础间隔很小
        
        if min_interval >= 20:
            print(f"✅ 最小间隔限制生效: {min_interval}s >= 20s")
        else:
            print(f"❌ 最小间隔限制失效: {min_interval}s < 20s")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ 错误处理测试失败: {e}")
        return False

def show_fix_summary():
    """显示修复总结"""
    print("\n" + "="*60)
    print("所有问题修复总结")
    print("="*60)
    
    print("""
🔧 修复的问题:

1. 回复间隔过短问题 ✅
   • 最小间隔: 20秒（防止被封号）
   • 智能调整: 根据回复数量动态增加间隔
   • 错误处理: 出错时增加等待时间
   • 随机因子: 避免规律性被检测

2. 程序报错修复 ✅
   • task_tree属性检查: 避免未初始化错误
   • 数据库方法补全: 添加缺失的方法
   • 布局管理器冲突: 修复grid/pack混用问题
   • 状态标签安全: 添加属性存在检查

3. 智能间隔策略 ✅
   • 前5次: 基础间隔（最少20秒）
   • 6-10次: 增加50%间隔
   • 11-20次: 增加100%间隔
   • 20次以上: 增加200%间隔
   • 有错误: 每个错误增加50%

4. 分段等待机制 ✅
   • 每5秒检查停止标志
   • 支持任务中途停止
   • 每30秒显示等待状态
   • 降级处理机制

🎯 安全机制:

• 防封号策略:
  - 最小20秒间隔
  - 动态间隔调整
  - 随机时间因子
  - 错误时延长等待

• 错误处理:
  - 属性存在检查
  - 异常捕获处理
  - 降级处理机制
  - 详细日志记录

• 用户体验:
  - 可中断等待
  - 状态实时显示
  - 错误友好提示
  - 界面稳定性

💡 使用建议:

• 回复间隔设置:
  - 建议设置30-60秒基础间隔
  - 系统会自动调整到安全范围
  - 错误时会自动延长等待

• 任务监控:
  - 观察回复成功率
  - 注意错误信息
  - 适时调整策略

• 安全使用:
  - 避免过于频繁的回复
  - 监控账号状态
  - 合理设置每日限制

⚠️ 重要提醒:

• 即使设置较短的间隔，系统也会强制最少20秒
• 连续错误会自动增加等待时间
• 建议监控回复成功率，及时调整策略
• 保持适度使用，避免被平台检测
""")

def main():
    """主测试函数"""
    print("🧪 所有修复测试")
    print("="*50)
    
    tests = [
        ("回复间隔改进", test_reply_interval_improvements),
        ("数据库修复", test_database_fixes),
        ("主窗口修复", test_main_window_fixes),
        ("任务监控修复", test_task_monitor_fixes),
        ("错误处理", test_error_handling)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        try:
            if test_func():
                passed += 1
                print(f"✅ {test_name} 测试通过")
            else:
                print(f"❌ {test_name} 测试失败")
        except Exception as e:
            print(f"❌ {test_name} 测试异常: {e}")
    
    print("\n" + "="*50)
    print(f"测试完成: {passed}/{total} 项测试通过")
    
    if passed >= 4:  # 允许1个测试失败
        print("🎉 所有问题修复完成！")
        show_fix_summary()
    else:
        print("❌ 部分测试失败，需要进一步检查。")
    
    return passed >= 4

if __name__ == "__main__":
    success = main()
    input("\n按回车键退出...")
    sys.exit(0 if success else 1)
