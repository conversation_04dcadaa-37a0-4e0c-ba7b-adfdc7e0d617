"""
辅助函数模块
"""
import re
import time
import random
from typing import List, Dict, Any, Optional
from datetime import datetime, timed<PERSON><PERSON>

def clean_text(text: str) -> str:
    """清理文本，移除特殊字符和多余空格"""
    if not text:
        return ""
    
    # 移除HTML标签
    text = re.sub(r'<[^>]+>', '', text)
    
    # 移除多余的空白字符
    text = re.sub(r'\s+', ' ', text)
    
    # 移除首尾空格
    text = text.strip()
    
    return text

def extract_note_id(url: str) -> Optional[str]:
    """从URL中提取笔记ID"""
    if not url:
        return None
    
    # 匹配小红书笔记ID格式
    pattern = r'/explore/([a-f0-9]{24})'
    match = re.search(pattern, url)
    
    if match:
        return match.group(1)
    
    # 尝试其他可能的格式
    pattern2 = r'note_id[=:]([a-f0-9]{24})'
    match2 = re.search(pattern2, url)
    
    if match2:
        return match2.group(1)
    
    return None

def is_valid_keyword(keyword: str) -> bool:
    """验证关键词是否有效"""
    if not keyword or not keyword.strip():
        return False
    
    # 长度限制
    if len(keyword.strip()) > 50:
        return False
    
    # 不能包含特殊字符
    if re.search(r'[<>"\']', keyword):
        return False
    
    return True

def generate_reply_content(templates: List[str], note_title: str = None, 
                          note_content: str = None) -> str:
    """生成回复内容"""
    if not templates:
        return "很棒的分享！👍"
    
    # 随机选择一个模板
    template = random.choice(templates)
    
    # 如果模板中包含占位符，尝试替换
    if note_title and "{title}" in template:
        template = template.replace("{title}", note_title[:20])
    
    if note_content and "{content}" in template:
        template = template.replace("{content}", note_content[:30])
    
    return template

def format_time_ago(timestamp: datetime) -> str:
    """格式化时间为"多久前"的形式"""
    if not timestamp:
        return "未知时间"
    
    now = datetime.now()
    if isinstance(timestamp, str):
        try:
            timestamp = datetime.fromisoformat(timestamp.replace('Z', '+00:00'))
        except:
            return "未知时间"
    
    diff = now - timestamp
    
    if diff.days > 0:
        return f"{diff.days}天前"
    elif diff.seconds > 3600:
        hours = diff.seconds // 3600
        return f"{hours}小时前"
    elif diff.seconds > 60:
        minutes = diff.seconds // 60
        return f"{minutes}分钟前"
    else:
        return "刚刚"

def parse_xiaohongshu_time(time_str: str) -> Optional[datetime]:
    """解析小红书时间格式"""
    if not time_str:
        return None
    
    try:
        # 处理"3小时前"、"昨天 23:56"等格式
        if "小时前" in time_str:
            hours = int(re.search(r'(\d+)小时前', time_str).group(1))
            return datetime.now() - timedelta(hours=hours)
        
        elif "分钟前" in time_str:
            minutes = int(re.search(r'(\d+)分钟前', time_str).group(1))
            return datetime.now() - timedelta(minutes=minutes)
        
        elif "天前" in time_str:
            days = int(re.search(r'(\d+)天前', time_str).group(1))
            return datetime.now() - timedelta(days=days)
        
        elif "昨天" in time_str:
            time_part = time_str.replace("昨天", "").strip()
            if time_part:
                yesterday = datetime.now() - timedelta(days=1)
                time_obj = datetime.strptime(time_part, "%H:%M")
                return yesterday.replace(hour=time_obj.hour, minute=time_obj.minute)
            else:
                return datetime.now() - timedelta(days=1)
        
        elif "刚刚" in time_str:
            return datetime.now()
        
        else:
            # 尝试直接解析时间格式
            return datetime.strptime(time_str, "%Y-%m-%d %H:%M:%S")
    
    except Exception:
        return None

def rate_limit_delay(min_delay: float = 1.0, max_delay: float = 3.0):
    """随机延迟，用于避免请求过于频繁"""
    delay = random.uniform(min_delay, max_delay)
    time.sleep(delay)

def validate_config(config: Dict[str, Any]) -> List[str]:
    """验证配置的有效性，返回错误列表"""
    errors = []
    
    # 检查必要的配置项
    required_keys = ['task.max_concurrent_tasks', 'task.search_interval', 'task.reply_interval']
    
    for key in required_keys:
        keys = key.split('.')
        value = config
        try:
            for k in keys:
                value = value[k]
            
            if key.endswith('_tasks') and (not isinstance(value, int) or value <= 0):
                errors.append(f"{key} 必须是正整数")
            elif key.endswith('_interval') and (not isinstance(value, (int, float)) or value < 0):
                errors.append(f"{key} 必须是非负数")
        
        except (KeyError, TypeError):
            errors.append(f"缺少必要配置项: {key}")
    
    return errors

def sanitize_filename(filename: str) -> str:
    """清理文件名，移除不安全字符"""
    # 移除或替换不安全的字符
    filename = re.sub(r'[<>:"/\\|?*]', '_', filename)
    
    # 限制长度
    if len(filename) > 100:
        filename = filename[:100]
    
    return filename

def chunk_list(lst: List, chunk_size: int) -> List[List]:
    """将列表分块"""
    return [lst[i:i + chunk_size] for i in range(0, len(lst), chunk_size)]

def retry_on_failure(max_retries: int = 3, delay: float = 1.0):
    """重试装饰器"""
    def decorator(func):
        def wrapper(*args, **kwargs):
            last_exception = None
            
            for attempt in range(max_retries):
                try:
                    return func(*args, **kwargs)
                except Exception as e:
                    last_exception = e
                    if attempt < max_retries - 1:
                        time.sleep(delay * (attempt + 1))
                    continue
            
            raise last_exception
        return wrapper
    return decorator

def is_business_hours(start_hour: int = 9, end_hour: int = 21) -> bool:
    """检查是否在营业时间内"""
    current_hour = datetime.now().hour
    return start_hour <= current_hour <= end_hour

def get_random_user_agent() -> str:
    """获取随机User-Agent"""
    user_agents = [
        "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/136.0.0.0 Safari/537.36",
        "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/135.0.0.0 Safari/537.36",
        "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/136.0.0.0 Safari/537.36",
        "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:109.0) Gecko/20100101 Firefox/109.0",
        "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.1 Safari/605.1.15"
    ]
    return random.choice(user_agents)
