"""
Web浏览器登录模块
"""
import time
import json
import threading
from typing import Dict, Optional, Callable
from selenium import webdriver
from selenium.webdriver.chrome.service import Service
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriver<PERSON>ait
from selenium.webdriver.support import expected_conditions as EC
from webdriver_manager.chrome import ChromeDriverManager

from config.database import db
from utils.logger import logger
from utils.crypto import cookie_manager

class WebAuth:
    """Web浏览器登录认证类"""
    
    def __init__(self):
        self.driver = None
        self.login_callback = None
        self.is_monitoring = False
        self.monitor_thread = None
        
    def set_login_callback(self, callback: Callable[[str, str], None]):
        """设置登录状态回调函数"""
        self.login_callback = callback
    
    def start_web_login(self) -> bool:
        """启动Web浏览器登录"""
        try:
            logger.info("启动Web浏览器登录...")
            
            # 配置Chrome选项
            chrome_options = Options()
            chrome_options.add_argument("--disable-blink-features=AutomationControlled")
            chrome_options.add_experimental_option("excludeSwitches", ["enable-automation"])
            chrome_options.add_experimental_option('useAutomationExtension', False)
            chrome_options.add_argument("--disable-extensions")
            chrome_options.add_argument("--no-sandbox")
            chrome_options.add_argument("--disable-dev-shm-usage")
            
            # 设置用户代理
            chrome_options.add_argument("--user-agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36")
            
            # 自动下载并配置ChromeDriver
            service = Service(ChromeDriverManager().install())
            
            # 创建WebDriver实例
            self.driver = webdriver.Chrome(service=service, options=chrome_options)
            
            # 执行反检测脚本
            self.driver.execute_script("Object.defineProperty(navigator, 'webdriver', {get: () => undefined})")
            
            # 打开小红书登录页面
            logger.info("打开小红书登录页面...")
            self.driver.get("https://www.xiaohongshu.com/")
            
            # 设置窗口标题
            self.driver.execute_script("document.title = '小红书登录 - 请手动完成登录操作';")
            
            # 启动Cookie监控
            self.start_cookie_monitoring()
            
            if self.login_callback:
                self.login_callback("browser_opened", "浏览器已打开，请手动完成登录")
            
            return True
            
        except Exception as e:
            error_msg = f"启动Web浏览器失败: {str(e)}"
            logger.error(error_msg)
            if self.login_callback:
                self.login_callback("error", error_msg)
            return False
    
    def start_cookie_monitoring(self):
        """启动Cookie监控"""
        if self.is_monitoring:
            return
        
        self.is_monitoring = True
        
        def monitor_cookies():
            """监控Cookie变化"""
            initial_cookies = {}
            login_detected = False
            start_time = time.time()

            # 等待页面完全加载并获取初始cookies
            time.sleep(3)
            try:
                for cookie in self.driver.get_cookies():
                    initial_cookies[cookie['name']] = cookie['value']
                logger.info(f"获取到初始Cookie: {len(initial_cookies)} 个")
            except:
                pass

            # 最小等待时间：2分钟，给用户充足时间操作
            min_wait_time = 120  # 2分钟

            while self.is_monitoring and self.driver:
                try:
                    current_time = time.time()
                    elapsed = int(current_time - start_time)

                    # 检查浏览器是否还在运行
                    current_url = self.driver.current_url

                    # 获取当前cookies
                    current_cookies = {}
                    for cookie in self.driver.get_cookies():
                        current_cookies[cookie['name']] = cookie['value']

                    # 在最小等待时间内，只显示等待状态，不进行登录检测
                    if elapsed < min_wait_time:
                        if elapsed % 30 == 0 and elapsed > 0:  # 每30秒更新一次
                            remaining = min_wait_time - elapsed
                            if self.login_callback:
                                self.login_callback("waiting", f"请在浏览器中完成登录操作... 还需等待至少 {remaining} 秒后开始检测")

                        time.sleep(5)  # 等待期间检查间隔更长
                        continue

                    # 超过最小等待时间后，开始进行登录检测
                    is_logged_in = self._check_login_status(current_url, current_cookies, initial_cookies)

                    if is_logged_in and not login_detected:
                        login_detected = True
                        logger.info("检测到用户已登录，正在获取Cookie...")

                        if self.login_callback:
                            self.login_callback("login_detected", "检测到登录成功，正在保存Cookie...")

                        # 等待页面稳定
                        time.sleep(3)

                        # 重新获取最新cookies
                        final_cookies = {}
                        for cookie in self.driver.get_cookies():
                            final_cookies[cookie['name']] = cookie['value']

                        logger.info(f"最终获取到Cookie: {len(final_cookies)} 个")

                        # 获取用户信息
                        user_info = self.extract_user_info()

                        # 保存cookies
                        self.save_login_cookies(final_cookies, user_info)

                        if self.login_callback:
                            self.login_callback("success", "登录成功，Cookie已保存")

                        # 延迟关闭浏览器，让用户看到成功消息
                        time.sleep(5)
                        self.close_browser()
                        break

                    # 定期更新等待状态
                    if elapsed % 30 == 0 and elapsed > min_wait_time:  # 每30秒更新一次
                        if self.login_callback:
                            self.login_callback("monitoring", f"正在检测登录状态... 已等待 {elapsed} 秒")

                    # 检查是否超时（30分钟）
                    if current_time - start_time > 1800:  # 30分钟超时
                        logger.warning("登录等待超时")
                        if self.login_callback:
                            self.login_callback("timeout", "登录等待超时，请重新尝试")
                        break

                    time.sleep(5)  # 每5秒检查一次
                    
                except Exception as e:
                    if "chrome not reachable" in str(e).lower():
                        logger.info("用户关闭了浏览器")
                        if self.login_callback:
                            self.login_callback("cancelled", "用户取消了登录")
                        break
                    else:
                        logger.error(f"Cookie监控异常: {str(e)}")
                        time.sleep(5)
            
            self.is_monitoring = False
        
        self.monitor_thread = threading.Thread(target=monitor_cookies, daemon=True)
        self.monitor_thread.start()

    def _check_login_status(self, current_url: str, current_cookies: Dict, initial_cookies: Dict) -> bool:
        """检查登录状态 - 更准确的检测方法"""
        try:
            # 方法1: 检查Cookie数量的显著增加
            cookie_increase = len(current_cookies) - len(initial_cookies)
            if cookie_increase < 3:  # Cookie增加数量不足，可能未登录
                return False

            # 方法2: 检查特定的登录Cookie（更严格）
            critical_login_cookies = ['web_session', 'a1', 'webId']
            login_cookie_count = sum(1 for cookie in critical_login_cookies if cookie in current_cookies)

            if login_cookie_count < 2:  # 至少需要2个关键登录Cookie
                return False

            # 方法3: 检查URL变化 - 登录后通常会跳转到首页或个人页面
            login_urls = [
                '/explore',  # 发现页面
                '/user/',    # 用户页面
                '/feed',     # 动态页面
                '/home'      # 首页
            ]

            url_indicates_login = any(url_pattern in current_url.lower() for url_pattern in login_urls)

            # 方法4: 检查页面标题
            try:
                page_title = self.driver.title
                login_titles = ['小红书', '发现', '首页', 'RedBook', 'Home', 'Explore']
                title_indicates_login = any(title in page_title for title in login_titles)

                # 排除登录页面的标题
                if '登录' in page_title or 'login' in page_title.lower():
                    title_indicates_login = False

            except:
                title_indicates_login = False

            # 方法5: 检查页面内容 - 查找登录后才有的元素
            try:
                # 查找用户头像、用户名等登录后才有的元素
                user_elements = self.driver.find_elements("css selector",
                    "[class*='avatar'], [class*='user'], [class*='profile'], [data-testid*='user']")
                has_user_elements = len(user_elements) > 0
            except:
                has_user_elements = False

            # 综合判断：需要满足多个条件
            conditions_met = 0

            if cookie_increase >= 5:  # Cookie显著增加
                conditions_met += 1
                logger.info(f"Cookie增加: {cookie_increase} 个")

            if login_cookie_count >= 2:  # 有关键登录Cookie
                conditions_met += 1
                logger.info(f"关键登录Cookie: {login_cookie_count} 个")

            if url_indicates_login:  # URL表明已登录
                conditions_met += 1
                logger.info(f"URL表明已登录: {current_url}")

            if title_indicates_login:  # 页面标题表明已登录
                conditions_met += 1
                logger.info(f"页面标题表明已登录: {page_title}")

            if has_user_elements:  # 有用户相关元素
                conditions_met += 1
                logger.info("检测到用户相关页面元素")

            # 需要至少满足3个条件才认为已登录
            is_logged_in = conditions_met >= 3

            if is_logged_in:
                logger.info(f"登录检测通过: 满足 {conditions_met}/5 个条件")
            else:
                logger.debug(f"登录检测未通过: 仅满足 {conditions_met}/5 个条件")

            return is_logged_in

        except Exception as e:
            logger.error(f"登录状态检测异常: {str(e)}")
            return False
    
    def extract_user_info(self) -> Dict:
        """提取用户信息"""
        try:
            user_info = {
                "user_id": "",
                "nickname": "Web登录用户",
                "avatar": ""
            }
            
            # 尝试从页面提取用户信息
            try:
                # 等待页面加载
                WebDriverWait(self.driver, 5).until(
                    EC.presence_of_element_located((By.TAG_NAME, "body"))
                )
                
                # 尝试获取用户昵称
                nickname_selectors = [
                    "[data-testid='user-name']",
                    ".user-name",
                    ".username",
                    "[class*='username']",
                    "[class*='nickname']"
                ]
                
                for selector in nickname_selectors:
                    try:
                        element = self.driver.find_element(By.CSS_SELECTOR, selector)
                        if element.text.strip():
                            user_info["nickname"] = element.text.strip()
                            break
                    except:
                        continue
                
                # 尝试从cookie获取用户ID
                cookies = self.driver.get_cookies()
                for cookie in cookies:
                    if cookie['name'] in ['userId', 'user_id', 'webId']:
                        user_info["user_id"] = cookie['value']
                        break
                
                if not user_info["user_id"]:
                    user_info["user_id"] = f"web_user_{int(time.time())}"
                
            except Exception as e:
                logger.warning(f"提取用户信息失败: {str(e)}")
            
            return user_info
            
        except Exception as e:
            logger.error(f"提取用户信息异常: {str(e)}")
            return {
                "user_id": f"web_user_{int(time.time())}",
                "nickname": "Web登录用户",
                "avatar": ""
            }
    
    def save_login_cookies(self, cookies: Dict, user_info: Dict):
        """保存登录Cookie"""
        try:
            # 主要保存到cookie.txt文件
            self.save_cookies_to_file(cookies, user_info)

            # 备用保存到数据库
            try:
                db.save_session(
                    user_id=user_info.get("user_id", ""),
                    cookies=cookies,
                    headers={}
                )
            except Exception as db_error:
                logger.warning(f"数据库保存失败: {str(db_error)}")

            # 备用保存到原有文件系统
            try:
                cookie_manager.save_cookies(cookies, user_info.get("user_id", ""))
            except Exception as file_error:
                logger.warning(f"原文件系统保存失败: {str(file_error)}")

            logger.info(f"登录Cookie已保存到cookie.txt: 用户 {user_info.get('nickname', 'Unknown')}")
            logger.info(f"共保存 {len(cookies)} 个Cookie")

        except Exception as e:
            logger.error(f"保存登录Cookie失败: {str(e)}")

    def save_cookies_to_file(self, cookies: Dict, user_info: Dict):
        """保存Cookie到cookie.txt文件"""
        try:
            import json
            import os
            from datetime import datetime

            cookie_data = {
                "user_info": user_info,
                "cookies": cookies,
                "save_time": datetime.now().isoformat(),
                "timestamp": int(time.time())
            }

            # 确保目录存在
            os.makedirs("data", exist_ok=True)

            # 保存到cookie.txt
            with open("data/cookie.txt", "w", encoding="utf-8") as f:
                json.dump(cookie_data, f, ensure_ascii=False, indent=2)

            logger.info("Cookie已保存到data/cookie.txt")

        except Exception as e:
            logger.error(f"保存Cookie到文件失败: {str(e)}")

    def load_cookies_from_file(self) -> Dict:
        """从cookie.txt文件加载Cookie"""
        try:
            import json
            import os

            cookie_file = "data/cookie.txt"
            if not os.path.exists(cookie_file):
                logger.info("cookie.txt文件不存在")
                return {}

            with open(cookie_file, "r", encoding="utf-8") as f:
                cookie_data = json.load(f)

            cookies = cookie_data.get("cookies", {})
            user_info = cookie_data.get("user_info", {})
            save_time = cookie_data.get("save_time", "")

            if cookies:
                logger.info(f"从cookie.txt加载Cookie成功: {len(cookies)}个, 保存时间: {save_time}")
                logger.info(f"用户信息: {user_info.get('nickname', 'Unknown')}")
                return cookie_data
            else:
                logger.warning("cookie.txt文件中没有有效的Cookie")
                return {}

        except Exception as e:
            logger.error(f"从cookie.txt加载Cookie失败: {str(e)}")
            return {}
    
    def close_browser(self):
        """关闭浏览器"""
        try:
            self.is_monitoring = False
            if self.driver:
                self.driver.quit()
                self.driver = None
                logger.info("浏览器已关闭")
        except Exception as e:
            logger.error(f"关闭浏览器失败: {str(e)}")
    
    def is_browser_open(self) -> bool:
        """检查浏览器是否还在运行"""
        try:
            if self.driver:
                self.driver.current_url
                return True
        except:
            pass
        return False

    def manual_check_login(self) -> Dict[str, str]:
        """手动检查登录状态"""
        try:
            if not self.driver:
                return {"status": "error", "message": "浏览器未启动"}

            # 获取当前cookies
            current_cookies = {}
            for cookie in self.driver.get_cookies():
                current_cookies[cookie['name']] = cookie['value']

            current_url = self.driver.current_url

            # 使用相同的严格检测逻辑
            # 这里我们假设初始cookie数量较少，手动检测时进行简化判断
            initial_cookies = {}  # 手动检测时假设初始为空

            is_logged_in = self._check_login_status(current_url, current_cookies, initial_cookies)

            if is_logged_in:
                # 手动触发登录成功流程
                user_info = self.extract_user_info()
                self.save_login_cookies(current_cookies, user_info)

                if self.login_callback:
                    self.login_callback("success", "手动检测到登录成功，Cookie已保存")

                return {"status": "success", "message": "登录成功"}

            else:
                # 提供详细的状态信息
                critical_cookies = ['web_session', 'a1', 'webId']
                found_cookies = [cookie for cookie in critical_cookies if cookie in current_cookies]

                return {
                    "status": "waiting",
                    "message": f"尚未检测到完整登录状态\n当前页面: {current_url[:50]}...\nCookie总数: {len(current_cookies)}\n关键Cookie: {found_cookies}"
                }

        except Exception as e:
            return {"status": "error", "message": f"检查失败: {str(e)}"}
    
    def get_login_instructions(self) -> str:
        """获取登录说明"""
        return """Web浏览器登录说明：

1. 程序将自动打开Chrome浏览器
2. 浏览器会导航到小红书官网
3. 请在浏览器中手动完成登录操作：
   - 点击登录按钮
   - 使用手机号/邮箱登录
   - 或扫描二维码登录
4. 登录成功后，程序会自动检测并保存Cookie
5. Cookie保存后浏览器会自动关闭

注意事项：
• 请确保已安装Chrome浏览器
• 首次使用会自动下载ChromeDriver
• 登录过程中请勿关闭浏览器窗口
• 如需取消登录，直接关闭浏览器即可"""

# 全局Web认证实例
web_auth = WebAuth()
