"""
Web浏览器登录模块
"""
import time
import json
import threading
from typing import Dict, Optional, Callable
from selenium import webdriver
from selenium.webdriver.chrome.service import Service
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriver<PERSON>ait
from selenium.webdriver.support import expected_conditions as EC
from webdriver_manager.chrome import ChromeDriverManager

from config.database import db
from utils.logger import logger
from utils.crypto import cookie_manager

class WebAuth:
    """Web浏览器登录认证类"""
    
    def __init__(self):
        self.driver = None
        self.login_callback = None
        self.is_monitoring = False
        self.monitor_thread = None
        
    def set_login_callback(self, callback: Callable[[str, str], None]):
        """设置登录状态回调函数"""
        self.login_callback = callback
    
    def start_web_login(self) -> bool:
        """启动Web浏览器登录"""
        try:
            logger.info("启动Web浏览器登录...")
            
            # 配置Chrome选项
            chrome_options = Options()
            chrome_options.add_argument("--disable-blink-features=AutomationControlled")
            chrome_options.add_experimental_option("excludeSwitches", ["enable-automation"])
            chrome_options.add_experimental_option('useAutomationExtension', False)
            chrome_options.add_argument("--disable-extensions")
            chrome_options.add_argument("--no-sandbox")
            chrome_options.add_argument("--disable-dev-shm-usage")
            
            # 设置用户代理
            chrome_options.add_argument("--user-agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36")
            
            # 自动下载并配置ChromeDriver
            service = Service(ChromeDriverManager().install())
            
            # 创建WebDriver实例
            self.driver = webdriver.Chrome(service=service, options=chrome_options)
            
            # 执行反检测脚本
            self.driver.execute_script("Object.defineProperty(navigator, 'webdriver', {get: () => undefined})")
            
            # 打开小红书登录页面
            logger.info("打开小红书登录页面...")
            self.driver.get("https://www.xiaohongshu.com/")
            
            # 设置窗口标题
            self.driver.execute_script("document.title = '小红书登录 - 请手动完成登录操作';")
            
            # 启动Cookie监控
            self.start_cookie_monitoring()
            
            if self.login_callback:
                self.login_callback("browser_opened", "浏览器已打开，请手动完成登录")
            
            return True
            
        except Exception as e:
            error_msg = f"启动Web浏览器失败: {str(e)}"
            logger.error(error_msg)
            if self.login_callback:
                self.login_callback("error", error_msg)
            return False
    
    def start_cookie_monitoring(self):
        """启动Cookie监控"""
        if self.is_monitoring:
            return
        
        self.is_monitoring = True
        
        def monitor_cookies():
            """监控Cookie变化"""
            last_cookies = {}
            login_detected = False
            start_time = time.time()
            last_activity_time = start_time

            while self.is_monitoring and self.driver:
                try:
                    # 检查浏览器是否还在运行
                    current_url = self.driver.current_url
                    current_time = time.time()

                    # 获取当前cookies
                    current_cookies = {}
                    for cookie in self.driver.get_cookies():
                        current_cookies[cookie['name']] = cookie['value']

                    # 检查cookies变化，更新活动时间
                    if current_cookies != last_cookies:
                        last_activity_time = current_time
                        logger.debug(f"Cookie变化检测: {len(current_cookies)} 个cookie")
                        last_cookies = current_cookies.copy()

                        if self.login_callback:
                            elapsed = int(current_time - start_time)
                            self.login_callback("monitoring", f"检测到页面活动，已等待 {elapsed} 秒...")

                    # 检查是否有登录相关的cookie
                    login_cookies = ['web_session', 'xsec_token', 'a1', 'webId', 'userId', 'user_id']
                    has_login_cookie = any(cookie_name in current_cookies for cookie_name in login_cookies)

                    # 更严格的登录检测条件
                    is_logged_in = False

                    # 方法1: 检查特定的登录cookie
                    if has_login_cookie and len(current_cookies) > 3:
                        is_logged_in = True
                        logger.info("检测到登录Cookie")

                    # 方法2: 检查URL变化（登录后通常会跳转）
                    elif any(keyword in current_url.lower() for keyword in ['user', 'explore', 'feed', 'home']) and 'xiaohongshu.com' in current_url:
                        if len(current_cookies) > 5:  # 确保有足够的cookie
                            is_logged_in = True
                            logger.info("检测到URL跳转和Cookie增加")

                    # 方法3: 检查页面标题变化
                    try:
                        page_title = self.driver.title
                        if any(keyword in page_title for keyword in ['首页', '发现', '用户', 'Home', 'Explore']) and len(current_cookies) > 5:
                            is_logged_in = True
                            logger.info("检测到页面标题变化")
                    except:
                        pass

                    if is_logged_in and not login_detected:
                        login_detected = True
                        logger.info("检测到用户已登录，正在获取Cookie...")

                        if self.login_callback:
                            self.login_callback("login_detected", "检测到登录成功，正在保存Cookie...")

                        # 等待页面稳定
                        time.sleep(2)

                        # 重新获取最新cookies
                        final_cookies = {}
                        for cookie in self.driver.get_cookies():
                            final_cookies[cookie['name']] = cookie['value']

                        # 获取用户信息
                        user_info = self.extract_user_info()

                        # 保存cookies
                        self.save_login_cookies(final_cookies, user_info)

                        if self.login_callback:
                            self.login_callback("success", "登录成功，Cookie已保存")

                        # 延迟关闭浏览器，让用户看到成功消息
                        time.sleep(5)
                        self.close_browser()
                        break

                    # 定期更新等待状态
                    elapsed = int(current_time - start_time)
                    if elapsed % 30 == 0 and elapsed > 0:  # 每30秒更新一次
                        if self.login_callback:
                            self.login_callback("waiting", f"等待用户登录中... 已等待 {elapsed} 秒")

                    # 检查是否超时（30分钟）
                    if current_time - start_time > 1800:  # 30分钟超时
                        logger.warning("登录等待超时")
                        if self.login_callback:
                            self.login_callback("timeout", "登录等待超时，请重新尝试")
                        break

                    time.sleep(3)  # 每3秒检查一次，减少CPU占用
                    
                except Exception as e:
                    if "chrome not reachable" in str(e).lower():
                        logger.info("用户关闭了浏览器")
                        if self.login_callback:
                            self.login_callback("cancelled", "用户取消了登录")
                        break
                    else:
                        logger.error(f"Cookie监控异常: {str(e)}")
                        time.sleep(5)
            
            self.is_monitoring = False
        
        self.monitor_thread = threading.Thread(target=monitor_cookies, daemon=True)
        self.monitor_thread.start()
    
    def extract_user_info(self) -> Dict:
        """提取用户信息"""
        try:
            user_info = {
                "user_id": "",
                "nickname": "Web登录用户",
                "avatar": ""
            }
            
            # 尝试从页面提取用户信息
            try:
                # 等待页面加载
                WebDriverWait(self.driver, 5).until(
                    EC.presence_of_element_located((By.TAG_NAME, "body"))
                )
                
                # 尝试获取用户昵称
                nickname_selectors = [
                    "[data-testid='user-name']",
                    ".user-name",
                    ".username",
                    "[class*='username']",
                    "[class*='nickname']"
                ]
                
                for selector in nickname_selectors:
                    try:
                        element = self.driver.find_element(By.CSS_SELECTOR, selector)
                        if element.text.strip():
                            user_info["nickname"] = element.text.strip()
                            break
                    except:
                        continue
                
                # 尝试从cookie获取用户ID
                cookies = self.driver.get_cookies()
                for cookie in cookies:
                    if cookie['name'] in ['userId', 'user_id', 'webId']:
                        user_info["user_id"] = cookie['value']
                        break
                
                if not user_info["user_id"]:
                    user_info["user_id"] = f"web_user_{int(time.time())}"
                
            except Exception as e:
                logger.warning(f"提取用户信息失败: {str(e)}")
            
            return user_info
            
        except Exception as e:
            logger.error(f"提取用户信息异常: {str(e)}")
            return {
                "user_id": f"web_user_{int(time.time())}",
                "nickname": "Web登录用户",
                "avatar": ""
            }
    
    def save_login_cookies(self, cookies: Dict, user_info: Dict):
        """保存登录Cookie"""
        try:
            # 保存到数据库
            db.save_session(
                user_id=user_info.get("user_id", ""),
                cookies=cookies,
                headers={}
            )
            
            # 保存到文件
            cookie_manager.save_cookies(cookies, user_info.get("user_id", ""))
            
            logger.info(f"登录Cookie已保存: 用户 {user_info.get('nickname', 'Unknown')}")
            logger.info(f"共保存 {len(cookies)} 个Cookie")
            
        except Exception as e:
            logger.error(f"保存登录Cookie失败: {str(e)}")
    
    def close_browser(self):
        """关闭浏览器"""
        try:
            self.is_monitoring = False
            if self.driver:
                self.driver.quit()
                self.driver = None
                logger.info("浏览器已关闭")
        except Exception as e:
            logger.error(f"关闭浏览器失败: {str(e)}")
    
    def is_browser_open(self) -> bool:
        """检查浏览器是否还在运行"""
        try:
            if self.driver:
                self.driver.current_url
                return True
        except:
            pass
        return False

    def manual_check_login(self) -> Dict[str, str]:
        """手动检查登录状态"""
        try:
            if not self.driver:
                return {"status": "error", "message": "浏览器未启动"}

            # 获取当前cookies
            current_cookies = {}
            for cookie in self.driver.get_cookies():
                current_cookies[cookie['name']] = cookie['value']

            # 检查登录状态
            login_cookies = ['web_session', 'xsec_token', 'a1', 'webId', 'userId', 'user_id']
            has_login_cookie = any(cookie_name in current_cookies for cookie_name in login_cookies)

            current_url = self.driver.current_url

            if has_login_cookie and len(current_cookies) > 3:
                # 手动触发登录成功流程
                user_info = self.extract_user_info()
                self.save_login_cookies(current_cookies, user_info)

                if self.login_callback:
                    self.login_callback("success", "手动检测到登录成功，Cookie已保存")

                return {"status": "success", "message": "登录成功"}

            else:
                return {
                    "status": "waiting",
                    "message": f"尚未检测到登录，当前页面: {current_url[:50]}..., Cookie数量: {len(current_cookies)}"
                }

        except Exception as e:
            return {"status": "error", "message": f"检查失败: {str(e)}"}
    
    def get_login_instructions(self) -> str:
        """获取登录说明"""
        return """Web浏览器登录说明：

1. 程序将自动打开Chrome浏览器
2. 浏览器会导航到小红书官网
3. 请在浏览器中手动完成登录操作：
   - 点击登录按钮
   - 使用手机号/邮箱登录
   - 或扫描二维码登录
4. 登录成功后，程序会自动检测并保存Cookie
5. Cookie保存后浏览器会自动关闭

注意事项：
• 请确保已安装Chrome浏览器
• 首次使用会自动下载ChromeDriver
• 登录过程中请勿关闭浏览器窗口
• 如需取消登录，直接关闭浏览器即可"""

# 全局Web认证实例
web_auth = WebAuth()
