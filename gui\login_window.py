"""
登录窗口界面
"""
import tkinter as tk
from tkinter import messagebox
import customtkinter as ctk
from PIL import Image, ImageTk
import base64
import io
import threading
import time

from core.auth import auth
from utils.logger import logger

class LoginWindow:
    """登录窗口类"""
    
    def __init__(self, parent, success_callback=None):
        self.parent = parent
        self.success_callback = success_callback
        
        # 创建窗口
        self.window = ctk.CTkToplevel(parent)
        self.setup_window()
        self.setup_ui()
        
        # 状态变量
        self.qr_code_image = None
        self.check_thread = None
        self.is_checking = False
        
    def setup_window(self):
        """设置窗口属性"""
        self.window.title("登录小红书")
        self.window.geometry("400x500")
        self.window.resizable(False, False)
        
        # 设置为模态窗口
        self.window.transient(self.parent)
        self.window.grab_set()
        
        # 居中显示
        self.center_window()
        
        # 设置关闭事件
        self.window.protocol("WM_DELETE_WINDOW", self.on_closing)
    
    def center_window(self):
        """窗口居中"""
        self.window.update_idletasks()
        x = (self.window.winfo_screenwidth() // 2) - (400 // 2)
        y = (self.window.winfo_screenheight() // 2) - (500 // 2)
        self.window.geometry(f"400x500+{x}+{y}")
    
    def setup_ui(self):
        """设置用户界面"""
        # 主框架
        main_frame = ctk.CTkFrame(self.window)
        main_frame.pack(fill="both", expand=True, padx=20, pady=20)
        
        # 标题
        title_label = ctk.CTkLabel(
            main_frame,
            text="小红书登录演示",
            font=ctk.CTkFont(size=20, weight="bold")
        )
        title_label.pack(pady=10)

        # 演示说明
        demo_label = ctk.CTkLabel(
            main_frame,
            text="当前为演示模式\n实际使用时会显示真实的小红书登录二维码",
            font=ctk.CTkFont(size=12),
            text_color="orange"
        )
        demo_label.pack(pady=5)
        
        # 二维码框架
        self.qr_frame = ctk.CTkFrame(main_frame)
        self.qr_frame.pack(pady=20)
        
        # 二维码标签
        self.qr_label = ctk.CTkLabel(
            self.qr_frame,
            text="正在生成二维码...",
            width=250,
            height=250
        )
        self.qr_label.pack(padx=20, pady=20)
        
        # 状态标签
        self.status_label = ctk.CTkLabel(
            main_frame,
            text="演示模式：等待30秒后可体验模拟登录",
            font=ctk.CTkFont(size=14)
        )
        self.status_label.pack(pady=10)
        
        # 按钮框架
        button_frame = ctk.CTkFrame(main_frame)
        button_frame.pack(pady=20)
        
        # 刷新按钮
        self.refresh_button = ctk.CTkButton(
            button_frame,
            text="刷新二维码",
            command=self.refresh_qr_code,
            width=120
        )
        self.refresh_button.pack(side="left", padx=10)
        
        # 取消按钮
        cancel_button = ctk.CTkButton(
            button_frame,
            text="取消",
            command=self.on_closing,
            width=120
        )
        cancel_button.pack(side="right", padx=10)
        
        # 自动生成二维码
        self.generate_qr_code()
    
    def generate_qr_code(self):
        """生成二维码"""
        def generate():
            try:
                self.update_status("正在生成二维码...")
                qr_image_data = auth.generate_qr_code()
                
                if qr_image_data:
                    # 在主线程中更新UI
                    self.window.after(0, lambda: self.display_qr_code(qr_image_data))
                    self.window.after(0, lambda: self.start_checking())
                else:
                    self.window.after(0, lambda: self.update_status("二维码生成失败，请重试"))
                    
            except Exception as e:
                logger.error(f"生成二维码异常: {str(e)}")
                self.window.after(0, lambda: self.update_status("生成二维码时发生错误"))
        
        threading.Thread(target=generate, daemon=True).start()
    
    def display_qr_code(self, qr_image_data):
        """显示二维码"""
        try:
            # 解码base64图片数据
            image_data = base64.b64decode(qr_image_data)
            image = Image.open(io.BytesIO(image_data))
            
            # 调整图片大小
            image = image.resize((200, 200), Image.Resampling.LANCZOS)
            
            # 转换为PhotoImage
            self.qr_code_image = ImageTk.PhotoImage(image)
            
            # 更新标签
            self.qr_label.configure(image=self.qr_code_image, text="")
            self.update_status("演示二维码已生成，等待30秒后可体验模拟登录")
            
        except Exception as e:
            logger.error(f"显示二维码异常: {str(e)}")
            self.update_status("显示二维码失败")
    
    def start_checking(self):
        """开始检查扫码状态"""
        if self.is_checking:
            return
        
        self.is_checking = True
        
        def check_status():
            try:
                while self.is_checking:
                    result = auth.check_qr_status()
                    status = result.get("status", "error")
                    message = result.get("message", "")
                    
                    if status == "success":
                        # 登录成功
                        user_info = result.get("user_info", {})
                        self.window.after(0, lambda: self.on_login_success(user_info))
                        break
                    
                    elif status == "scanned":
                        # 已扫描，等待确认
                        self.window.after(0, lambda: self.update_status("已扫描，请在手机上确认登录"))
                    
                    elif status == "expired":
                        # 二维码过期
                        self.window.after(0, lambda: self.update_status("二维码已过期，请刷新"))
                        self.is_checking = False
                        break
                    
                    elif status == "waiting":
                        # 等待扫描
                        self.window.after(0, lambda: self.update_status("等待扫描二维码..."))
                    
                    else:
                        # 错误
                        self.window.after(0, lambda: self.update_status(f"检查状态失败: {message}"))
                        self.is_checking = False
                        break
                    
                    # 等待2秒后再次检查
                    time.sleep(2)
                    
            except Exception as e:
                logger.error(f"检查扫码状态异常: {str(e)}")
                self.window.after(0, lambda: self.update_status("检查登录状态时发生错误"))
                self.is_checking = False
        
        self.check_thread = threading.Thread(target=check_status, daemon=True)
        self.check_thread.start()
    
    def stop_checking(self):
        """停止检查"""
        self.is_checking = False
    
    def refresh_qr_code(self):
        """刷新二维码"""
        self.stop_checking()
        self.qr_label.configure(image="", text="正在生成二维码...")
        self.generate_qr_code()
    
    def update_status(self, message):
        """更新状态"""
        self.status_label.configure(text=message)
        logger.info(f"登录状态: {message}")
    
    def on_login_success(self, user_info):
        """登录成功处理"""
        self.stop_checking()
        self.update_status("登录成功！")
        
        # 显示成功消息
        messagebox.showinfo("登录成功", f"欢迎，{user_info.get('nickname', '用户')}！")
        
        # 调用成功回调
        if self.success_callback:
            self.success_callback(user_info)
        
        # 关闭窗口
        self.window.after(1000, self.window.destroy)
    
    def on_closing(self):
        """窗口关闭事件"""
        self.stop_checking()
        self.window.destroy()
