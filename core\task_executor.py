"""
任务执行引擎 - 基于真实抓包数据实现自动回帖
"""
import time
import threading
import random
from typing import Dict, List, Any, Optional
from datetime import datetime

from config.database import db
from utils.logger import logger
from core.search import search_engine
from core.reply import reply_engine
from core.auth import auth

class TaskExecutor:
    """任务执行引擎"""
    
    def __init__(self):
        self.running_tasks = {}  # 正在运行的任务
        self.task_threads = {}   # 任务线程
        self.stop_flags = {}     # 停止标志
        
    def start_task(self, task_id: int) -> bool:
        """启动任务"""
        try:
            # 检查登录状态
            if not auth.is_logged_in():
                logger.error("用户未登录，无法启动任务")
                return False
            
            # 检查任务是否已在运行
            if task_id in self.running_tasks:
                logger.warning(f"任务 {task_id} 已在运行中")
                return False
            
            # 获取任务配置
            task_config = db.get_task(task_id)
            if not task_config:
                logger.error(f"任务 {task_id} 不存在")
                return False
            
            # 创建停止标志
            self.stop_flags[task_id] = threading.Event()
            
            # 创建并启动任务线程
            task_thread = threading.Thread(
                target=self._execute_task,
                args=(task_id, task_config),
                daemon=True,
                name=f"Task-{task_id}"
            )
            
            self.task_threads[task_id] = task_thread
            self.running_tasks[task_id] = {
                "start_time": datetime.now(),
                "status": "running",
                "config": task_config,
                "stats": {
                    "search_count": 0,
                    "reply_count": 0,
                    "error_count": 0
                }
            }
            
            task_thread.start()
            
            logger.info(f"任务 {task_id} 启动成功: {task_config.get('name', 'Unknown')}")
            return True
            
        except Exception as e:
            logger.error(f"启动任务 {task_id} 失败: {str(e)}")
            return False
    
    def stop_task(self, task_id: int) -> bool:
        """停止任务"""
        try:
            if task_id not in self.running_tasks:
                logger.warning(f"任务 {task_id} 未在运行")
                return False
            
            # 设置停止标志
            if task_id in self.stop_flags:
                self.stop_flags[task_id].set()
            
            # 等待线程结束
            if task_id in self.task_threads:
                thread = self.task_threads[task_id]
                thread.join(timeout=5)  # 最多等待5秒
            
            # 清理资源
            self._cleanup_task(task_id)
            
            logger.info(f"任务 {task_id} 已停止")
            return True
            
        except Exception as e:
            logger.error(f"停止任务 {task_id} 失败: {str(e)}")
            return False
    
    def _execute_task(self, task_id: int, task_config: Dict[str, Any]):
        """执行任务主循环"""
        try:
            task_name = task_config.get("name", f"Task-{task_id}")
            keywords = task_config.get("keywords", [])
            config = task_config.get("config", {})
            
            logger.info(f"开始执行任务: {task_name}")
            logger.info(f"关键词: {keywords}")
            
            # 任务配置
            search_interval = config.get("search_interval", 60)  # 搜索间隔
            reply_interval = config.get("reply_interval", 30)    # 回复间隔
            max_daily_replies = config.get("max_daily_replies", 50)
            max_replies_per_note = config.get("max_replies_per_note", 1)
            min_like_count = config.get("min_like_count", 0)
            max_like_count = config.get("max_like_count", 10000)
            
            # 回复配置
            reply_template = config.get("reply_template", "默认模板")
            custom_reply = config.get("custom_reply", "")
            
            last_search_time = 0
            daily_reply_count = 0
            
            while not self.stop_flags[task_id].is_set():
                try:
                    current_time = time.time()
                    
                    # 检查是否需要搜索
                    if current_time - last_search_time >= search_interval:
                        logger.info(f"任务 {task_id}: 开始搜索...")
                        
                        # 随机选择一个关键词进行搜索
                        keyword = random.choice(keywords)
                        
                        # 执行搜索
                        search_result = search_engine.search_notes(keyword, page=1)
                        
                        if search_result.get("success"):
                            notes = search_result.get("notes", [])
                            logger.info(f"任务 {task_id}: 搜索到 {len(notes)} 个笔记")
                            
                            # 更新统计
                            self.running_tasks[task_id]["stats"]["search_count"] += 1
                            
                            # 处理搜索结果
                            for note in notes:
                                # 检查停止标志
                                if self.stop_flags[task_id].is_set():
                                    break
                                
                                # 检查每日回复限制
                                if daily_reply_count >= max_daily_replies:
                                    logger.info(f"任务 {task_id}: 已达到每日最大回复数 {max_daily_replies}")
                                    break
                                
                                # 检查笔记是否符合条件
                                if self._should_reply_to_note(note, config):
                                    # 执行回复
                                    reply_result = self._reply_to_note(
                                        note, 
                                        reply_template, 
                                        custom_reply,
                                        task_id
                                    )
                                    
                                    if reply_result.get("success"):
                                        daily_reply_count += 1
                                        self.running_tasks[task_id]["stats"]["reply_count"] += 1
                                        logger.info(f"任务 {task_id}: 回复成功，今日已回复 {daily_reply_count} 次")
                                        
                                        # 回复间隔
                                        time.sleep(reply_interval)
                                    else:
                                        self.running_tasks[task_id]["stats"]["error_count"] += 1
                                        logger.error(f"任务 {task_id}: 回复失败 - {reply_result.get('error')}")
                        else:
                            logger.error(f"任务 {task_id}: 搜索失败 - {search_result.get('error')}")
                            self.running_tasks[task_id]["stats"]["error_count"] += 1
                        
                        last_search_time = current_time
                    
                    # 短暂休眠，避免CPU占用过高
                    time.sleep(5)
                    
                except Exception as e:
                    logger.error(f"任务 {task_id} 执行异常: {str(e)}")
                    self.running_tasks[task_id]["stats"]["error_count"] += 1
                    time.sleep(10)  # 出错后等待更长时间
            
            logger.info(f"任务 {task_id} 执行完成")
            
        except Exception as e:
            logger.error(f"任务 {task_id} 执行失败: {str(e)}")
        finally:
            # 清理任务
            self._cleanup_task(task_id)
    
    def _should_reply_to_note(self, note: Dict[str, Any], config: Dict[str, Any]) -> bool:
        """判断是否应该回复该笔记"""
        try:
            note_id = note.get("id", "")
            if not note_id:
                return False
            
            # 检查是否已经回复过
            if self._has_replied_to_note(note_id):
                return False
            
            # 检查点赞数范围
            stats = note.get("stats", {})
            like_count = stats.get("liked_count", 0)
            min_likes = config.get("min_like_count", 0)
            max_likes = config.get("max_like_count", 10000)
            
            if not (min_likes <= like_count <= max_likes):
                logger.debug(f"笔记 {note_id} 点赞数 {like_count} 不在范围 [{min_likes}, {max_likes}] 内")
                return False
            
            # 检查笔记类型（可以添加更多过滤条件）
            note_type = note.get("type", "")
            if note_type == "video" and not config.get("reply_to_videos", True):
                return False
            
            return True
            
        except Exception as e:
            logger.error(f"判断是否回复笔记失败: {str(e)}")
            return False
    
    def _has_replied_to_note(self, note_id: str) -> bool:
        """检查是否已经回复过该笔记"""
        try:
            # 查询数据库中的回复记录
            return db.has_replied_to_note(note_id)
        except Exception as e:
            logger.error(f"检查回复记录失败: {str(e)}")
            return False
    
    def _reply_to_note(self, note: Dict[str, Any], template: str, custom_reply: str, task_id: int) -> Dict[str, Any]:
        """回复笔记"""
        try:
            note_id = note.get("id", "")
            note_title = note.get("title", "")
            
            # 生成回复内容
            if custom_reply:
                reply_content = self._process_reply_template(custom_reply, note)
            else:
                reply_content = self._generate_reply_content(template, note)
            
            logger.info(f"任务 {task_id}: 准备回复笔记 {note_id}, 内容: {reply_content}")
            
            # 调用回复引擎
            result = reply_engine.reply_to_note(
                note_id=note_id,
                note_title=note_title,
                custom_reply=reply_content
            )
            
            return result
            
        except Exception as e:
            logger.error(f"回复笔记失败: {str(e)}")
            return {"success": False, "error": str(e)}
    
    def _generate_reply_content(self, template: str, note: Dict[str, Any]) -> str:
        """生成回复内容"""
        try:
            # 简单的回复模板
            templates = [
                "很棒的分享！👍",
                "学到了，感谢分享！✨",
                "太有用了，收藏了！💯",
                "写得很好，支持一下！🔥",
                "很实用的内容，谢谢！🙌"
            ]
            
            return random.choice(templates)
            
        except Exception as e:
            logger.error(f"生成回复内容失败: {str(e)}")
            return "很棒的分享！👍"
    
    def _process_reply_template(self, template: str, note: Dict[str, Any]) -> str:
        """处理回复模板中的变量"""
        try:
            content = template
            
            # 替换变量
            content = content.replace("{title}", note.get("title", "")[:20])
            content = content.replace("{author}", note.get("author", {}).get("nickname", ""))
            
            return content
            
        except Exception as e:
            logger.error(f"处理回复模板失败: {str(e)}")
            return template
    
    def _cleanup_task(self, task_id: int):
        """清理任务资源"""
        try:
            if task_id in self.running_tasks:
                del self.running_tasks[task_id]
            
            if task_id in self.task_threads:
                del self.task_threads[task_id]
            
            if task_id in self.stop_flags:
                del self.stop_flags[task_id]
                
        except Exception as e:
            logger.error(f"清理任务 {task_id} 资源失败: {str(e)}")
    
    def get_task_status(self, task_id: int) -> Optional[Dict[str, Any]]:
        """获取任务状态"""
        return self.running_tasks.get(task_id)
    
    def get_all_running_tasks(self) -> Dict[int, Dict[str, Any]]:
        """获取所有运行中的任务"""
        return self.running_tasks.copy()
    
    def stop_all_tasks(self):
        """停止所有任务"""
        task_ids = list(self.running_tasks.keys())
        for task_id in task_ids:
            self.stop_task(task_id)

# 全局任务执行器实例
task_executor = TaskExecutor()
