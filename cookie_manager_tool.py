"""
Cookie管理工具
用于管理cookie.txt文件
"""
import sys
import os
import json
from pathlib import Path
from datetime import datetime

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def check_cookie_file():
    """检查cookie.txt文件"""
    print("🍪 检查cookie.txt文件...")
    
    cookie_file = "data/cookie.txt"
    
    if not os.path.exists(cookie_file):
        print("❌ cookie.txt文件不存在")
        print("💡 请先完成Web登录以生成cookie.txt文件")
        return False
    
    try:
        with open(cookie_file, "r", encoding="utf-8") as f:
            cookie_data = json.load(f)
        
        print("✅ cookie.txt文件存在且格式正确")
        
        # 显示基本信息
        user_info = cookie_data.get("user_info", {})
        cookies = cookie_data.get("cookies", {})
        save_time = cookie_data.get("save_time", "")
        timestamp = cookie_data.get("timestamp", 0)
        
        print(f"📅 保存时间: {save_time}")
        print(f"👤 用户信息: {user_info.get('nickname', 'Unknown')}")
        print(f"🍪 Cookie数量: {len(cookies)}")
        
        # 检查关键Cookie
        critical_cookies = ['web_session', 'a1', 'webId', 'userId', 'xsec_token']
        present_cookies = []
        
        for key in critical_cookies:
            if key in cookies:
                value = cookies[key]
                # 只显示前10个字符，保护隐私
                display_value = value[:10] + "..." if len(value) > 10 else value
                present_cookies.append(f"{key}: {display_value}")
        
        if present_cookies:
            print("🔑 关键Cookie:")
            for cookie_info in present_cookies:
                print(f"   {cookie_info}")
        
        # 判断Cookie完整性
        has_critical = sum(1 for key in critical_cookies if key in cookies)
        
        if has_critical >= 3:
            print("✅ Cookie完整，应该能够保持登录状态")
            return True
        else:
            print("⚠️ Cookie不完整，可能需要重新登录")
            return False
            
    except Exception as e:
        print(f"❌ 读取cookie.txt文件失败: {e}")
        return False

def test_cookie_loading():
    """测试Cookie加载"""
    print("\n🔄 测试Cookie加载...")
    
    try:
        from core.auth import auth
        
        # 测试从cookie.txt加载
        cookie_data = auth.load_cookies_from_txt()
        
        if cookie_data:
            cookies = cookie_data.get("cookies", {})
            user_info = cookie_data.get("user_info", {})
            
            print(f"✅ 成功从cookie.txt加载数据")
            print(f"👤 用户: {user_info.get('nickname', 'Unknown')}")
            print(f"🍪 Cookie数量: {len(cookies)}")
            
            # 测试加载到session
            result = auth.load_saved_session()
            if result:
                print("✅ Cookie已成功加载到认证模块")
                
                # 测试登录状态
                is_logged_in = auth.is_logged_in()
                print(f"🔐 登录状态: {'已登录' if is_logged_in else '未登录'}")
                
                return is_logged_in
            else:
                print("❌ Cookie加载到认证模块失败")
                return False
        else:
            print("❌ 无法从cookie.txt加载数据")
            return False
            
    except Exception as e:
        print(f"❌ Cookie加载测试失败: {e}")
        return False

def backup_cookie_file():
    """备份cookie.txt文件"""
    print("\n💾 备份cookie.txt文件...")
    
    try:
        cookie_file = "data/cookie.txt"
        if not os.path.exists(cookie_file):
            print("❌ cookie.txt文件不存在，无法备份")
            return False
        
        # 创建备份文件名
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        backup_file = f"data/cookie_backup_{timestamp}.txt"
        
        # 复制文件
        import shutil
        shutil.copy2(cookie_file, backup_file)
        
        print(f"✅ Cookie文件已备份到: {backup_file}")
        return True
        
    except Exception as e:
        print(f"❌ 备份失败: {e}")
        return False

def clear_cookie_file():
    """清除cookie.txt文件"""
    print("\n🗑️ 清除cookie.txt文件...")
    
    try:
        cookie_file = "data/cookie.txt"
        if os.path.exists(cookie_file):
            # 先备份
            backup_cookie_file()
            
            # 删除文件
            os.remove(cookie_file)
            print("✅ cookie.txt文件已删除")
            print("💡 下次登录时会重新生成")
            return True
        else:
            print("ℹ️ cookie.txt文件不存在")
            return True
            
    except Exception as e:
        print(f"❌ 清除失败: {e}")
        return False

def show_cookie_content():
    """显示cookie.txt文件内容"""
    print("\n📄 显示cookie.txt文件内容...")
    
    try:
        cookie_file = "data/cookie.txt"
        if not os.path.exists(cookie_file):
            print("❌ cookie.txt文件不存在")
            return False
        
        with open(cookie_file, "r", encoding="utf-8") as f:
            content = f.read()
        
        print("📄 cookie.txt文件内容:")
        print("-" * 50)
        
        # 解析并美化显示
        try:
            cookie_data = json.loads(content)
            print(json.dumps(cookie_data, ensure_ascii=False, indent=2))
        except:
            print(content)
        
        print("-" * 50)
        return True
        
    except Exception as e:
        print(f"❌ 显示内容失败: {e}")
        return False

def show_usage_guide():
    """显示使用指南"""
    print("\n" + "="*60)
    print("Cookie持久化使用指南")
    print("="*60)
    
    print("""
🍪 Cookie文件持久化机制:

1. 保存位置: data/cookie.txt
2. 保存时机: Web登录成功后自动保存
3. 加载时机: 程序启动时自动加载
4. 文件格式: JSON格式，包含用户信息和Cookie

📋 使用流程:
1. 首次登录:
   python main.py → 点击"登录" → 浏览器登录 → 自动保存到cookie.txt

2. 后续启动:
   python main.py → 自动从cookie.txt加载 → 保持登录状态

🔧 管理命令:
• 检查Cookie: python cookie_manager_tool.py check
• 测试加载: python cookie_manager_tool.py test
• 备份Cookie: python cookie_manager_tool.py backup
• 清除Cookie: python cookie_manager_tool.py clear
• 显示内容: python cookie_manager_tool.py show

⚠️ 注意事项:
• cookie.txt包含敏感信息，请妥善保管
• Cookie有时效性，过期后需要重新登录
• 删除cookie.txt会清除登录状态
• 建议定期备份cookie.txt文件

🔄 故障排除:
• 如果登录状态丢失，删除cookie.txt重新登录
• 如果加载失败，检查文件格式是否正确
• 如果持久化不工作，查看程序日志
""")

def main():
    """主函数"""
    if len(sys.argv) > 1:
        command = sys.argv[1].lower()
        
        if command == "check":
            check_cookie_file()
        elif command == "test":
            test_cookie_loading()
        elif command == "backup":
            backup_cookie_file()
        elif command == "clear":
            clear_cookie_file()
        elif command == "show":
            show_cookie_content()
        else:
            print(f"未知命令: {command}")
            print("可用命令: check, test, backup, clear, show")
    else:
        # 默认执行完整检查
        print("🧪 Cookie管理工具")
        print("="*50)
        
        tests = [
            ("检查Cookie文件", check_cookie_file),
            ("测试Cookie加载", test_cookie_loading)
        ]
        
        passed = 0
        total = len(tests)
        
        for test_name, test_func in tests:
            try:
                print(f"\n🔍 {test_name}...")
                if test_func():
                    passed += 1
                    print(f"✅ {test_name} 通过")
                else:
                    print(f"❌ {test_name} 失败")
            except Exception as e:
                print(f"❌ {test_name} 异常: {e}")
        
        print("\n" + "="*50)
        print(f"检查完成: {passed}/{total} 项通过")
        
        if passed == total:
            print("🎉 Cookie持久化工作正常！")
        elif passed > 0:
            print("⚠️ Cookie存在但可能有问题")
        else:
            print("❌ Cookie持久化未工作，请重新登录")
        
        show_usage_guide()

if __name__ == "__main__":
    main()
    input("\n按回车键退出...")
