"""
Cookie问题调试工具
"""
import sys
import os
import json
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def check_cookie_file_detailed():
    """详细检查cookie.txt文件"""
    print("🔍 详细检查cookie.txt文件...")
    
    cookie_file = "data/cookie.txt"
    
    if not os.path.exists(cookie_file):
        print("❌ cookie.txt文件不存在")
        return False
    
    try:
        with open(cookie_file, "r", encoding="utf-8") as f:
            cookie_data = json.load(f)
        
        print("✅ cookie.txt文件存在且格式正确")
        
        # 显示详细信息
        user_info = cookie_data.get("user_info", {})
        cookies = cookie_data.get("cookies", {})
        save_time = cookie_data.get("save_time", "")
        timestamp = cookie_data.get("timestamp", 0)
        
        print(f"\n📋 文件信息:")
        print(f"   保存时间: {save_time}")
        print(f"   时间戳: {timestamp}")
        print(f"   用户昵称: {user_info.get('nickname', 'Unknown')}")
        print(f"   用户ID: {user_info.get('user_id', 'Unknown')}")
        print(f"   Cookie总数: {len(cookies)}")
        
        # 检查关键Cookie
        critical_cookies = ['web_session', 'a1', 'webId', 'userId', 'xsec_token']
        print(f"\n🔑 关键Cookie检查:")
        
        for key in critical_cookies:
            if key in cookies:
                value = cookies[key]
                display_value = value[:15] + "..." if len(value) > 15 else value
                print(f"   ✅ {key}: {display_value}")
            else:
                print(f"   ❌ {key}: 缺失")
        
        # 显示所有Cookie名称
        print(f"\n📝 所有Cookie名称:")
        for i, cookie_name in enumerate(cookies.keys(), 1):
            print(f"   {i:2d}. {cookie_name}")
        
        return True
        
    except Exception as e:
        print(f"❌ 读取cookie.txt失败: {e}")
        return False

def test_cookie_loading_step_by_step():
    """逐步测试Cookie加载过程"""
    print("\n🔄 逐步测试Cookie加载过程...")
    
    try:
        # 步骤1: 测试文件读取
        print("步骤1: 测试文件读取...")
        from core.auth import auth
        
        cookie_data = auth.load_cookies_from_txt()
        if cookie_data:
            print("✅ 文件读取成功")
            cookies = cookie_data.get("cookies", {})
            print(f"   读取到 {len(cookies)} 个Cookie")
        else:
            print("❌ 文件读取失败")
            return False
        
        # 步骤2: 测试Cookie更新到session
        print("\n步骤2: 测试Cookie更新到session...")
        auth.session.cookies.clear()  # 清空现有cookies
        auth.session.cookies.update(cookies)
        
        session_cookies = dict(auth.session.cookies)
        print(f"✅ Session中现有 {len(session_cookies)} 个Cookie")
        
        # 步骤3: 测试基本Cookie检查
        print("\n步骤3: 测试基本Cookie检查...")
        has_valid = auth.has_valid_cookies()
        print(f"{'✅' if has_valid else '❌'} 基本Cookie检查: {has_valid}")
        
        if not has_valid:
            # 显示详细的Cookie检查信息
            critical_cookies = ['web_session', 'a1', 'webId']
            present_cookies = [key for key in critical_cookies if key in session_cookies and session_cookies[key]]
            print(f"   关键Cookie存在: {present_cookies}")
            print(f"   需要至少2个，实际有: {len(present_cookies)}")
        
        # 步骤4: 测试完整验证
        print("\n步骤4: 测试完整验证...")
        is_valid = auth.verify_session()
        print(f"{'✅' if is_valid else '❌'} 完整验证结果: {is_valid}")
        
        return is_valid
        
    except Exception as e:
        print(f"❌ Cookie加载测试失败: {e}")
        return False

def test_manual_cookie_validation():
    """手动测试Cookie有效性"""
    print("\n🧪 手动测试Cookie有效性...")
    
    try:
        import requests
        from core.auth import auth
        
        # 加载Cookie
        cookie_data = auth.load_cookies_from_txt()
        if not cookie_data:
            print("❌ 无法加载Cookie数据")
            return False
        
        cookies = cookie_data.get("cookies", {})
        
        # 创建测试session
        test_session = requests.Session()
        test_session.cookies.update(cookies)
        
        # 尝试简单的请求
        print("测试1: 尝试访问小红书主页...")
        try:
            response = test_session.get("https://www.xiaohongshu.com/", timeout=10)
            print(f"   状态码: {response.status_code}")
            
            if response.status_code == 200:
                print("✅ 主页访问成功")
            else:
                print(f"⚠️ 主页访问异常: {response.status_code}")
        except Exception as e:
            print(f"❌ 主页访问失败: {e}")
        
        # 尝试API请求（不带签名）
        print("\n测试2: 尝试简单API请求...")
        try:
            api_url = "https://edith.xiaohongshu.com/api/sns/web/v1/user/info"
            headers = {
                "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36",
                "Referer": "https://www.xiaohongshu.com/"
            }
            
            response = test_session.get(api_url, headers=headers, timeout=10)
            print(f"   状态码: {response.status_code}")
            
            if response.status_code == 200:
                try:
                    data = response.json()
                    print(f"   响应: {data}")
                    if data.get("success") or data.get("code") == 0:
                        print("✅ API请求成功，Cookie有效")
                        return True
                    else:
                        print(f"⚠️ API返回错误: {data.get('msg', 'Unknown')}")
                except:
                    print("⚠️ 响应不是JSON格式")
            else:
                print(f"⚠️ API请求失败: {response.status_code}")
                
        except Exception as e:
            print(f"❌ API请求异常: {e}")
        
        return False
        
    except Exception as e:
        print(f"❌ 手动验证失败: {e}")
        return False

def suggest_solutions():
    """建议解决方案"""
    print("\n" + "="*60)
    print("Cookie失效问题解决方案")
    print("="*60)
    
    print("""
🔍 可能的原因:

1. Cookie确实已过期
   • 小红书的Cookie有时效性
   • 长时间未使用会自动过期

2. 验证逻辑过于严格
   • API接口可能需要特殊签名
   • 请求头不完整

3. 网络连接问题
   • API请求超时
   • 网络不稳定

🛠️ 解决方案:

方案1: 重新登录 (推荐)
   python main.py
   # 点击登录，重新扫码

方案2: 清除Cookie重新开始
   python cookie_manager_tool.py clear
   python main.py

方案3: 使用宽松验证模式
   # 已经实现，现在会优先检查Cookie存在性
   # 而不是立即进行API验证

方案4: 检查网络连接
   # 确保能正常访问小红书网站
   # 检查防火墙和代理设置

💡 建议操作顺序:
1. 运行此调试工具查看详细信息
2. 如果Cookie存在但验证失败，尝试重新登录
3. 如果问题持续，清除Cookie重新开始
4. 检查网络连接和防火墙设置
""")

def main():
    """主函数"""
    print("🐛 Cookie问题调试工具")
    print("="*50)
    
    tests = [
        ("检查Cookie文件", check_cookie_file_detailed),
        ("逐步测试加载", test_cookie_loading_step_by_step),
        ("手动验证Cookie", test_manual_cookie_validation)
    ]
    
    results = []
    
    for test_name, test_func in tests:
        try:
            print(f"\n{'='*20} {test_name} {'='*20}")
            result = test_func()
            results.append((test_name, result))
            print(f"{'✅' if result else '❌'} {test_name}: {'通过' if result else '失败'}")
        except Exception as e:
            print(f"❌ {test_name} 异常: {e}")
            results.append((test_name, False))
    
    # 总结
    print("\n" + "="*50)
    print("调试结果总结:")
    
    passed = sum(1 for _, result in results if result)
    total = len(results)
    
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"  {test_name}: {status}")
    
    print(f"\n总计: {passed}/{total} 项测试通过")
    
    if passed == 0:
        print("\n🚨 所有测试都失败，建议重新登录")
    elif passed < total:
        print("\n⚠️ 部分测试失败，Cookie可能有问题")
    else:
        print("\n🎉 所有测试通过，Cookie应该是有效的")
    
    # 显示解决方案
    suggest_solutions()

if __name__ == "__main__":
    main()
    input("\n按回车键退出...")
