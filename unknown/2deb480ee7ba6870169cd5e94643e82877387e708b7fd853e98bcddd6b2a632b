"""
检查Cookie持久化状态
"""
import sys
import os
import json
import time
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def check_saved_cookies():
    """检查保存的Cookies"""
    print("🔍 检查保存的Cookies...")
    
    cookie_file = Path("data/cookies.json")
    
    if not cookie_file.exists():
        print("❌ 未找到保存的Cookies文件")
        print("💡 请先完成Web登录以保存Cookies")
        return False
    
    try:
        with open(cookie_file, 'r', encoding='utf-8') as f:
            cookie_data = json.load(f)
        
        print("✅ 找到Cookies文件")
        print(f"📅 保存时间: {time.strftime('%Y-%m-%d %H:%M:%S', time.localtime(cookie_data.get('timestamp', 0)))}")
        print(f"👤 用户ID: {cookie_data.get('user_id', 'N/A')}")
        
        cookies = cookie_data.get('cookies', {})
        print(f"🍪 Cookies数量: {len(cookies)}")
        
        # 检查关键Cookies
        key_cookies = ['web_session', 'a1', 'webId', 'userId', 'xsec_token']
        present_cookies = []
        
        for key in key_cookies:
            if key in cookies:
                value = cookies[key]
                # 只显示前10个字符，保护隐私
                display_value = value[:10] + "..." if len(value) > 10 else value
                present_cookies.append(f"{key}: {display_value}")
        
        if present_cookies:
            print("🔑 关键Cookies:")
            for cookie_info in present_cookies:
                print(f"   {cookie_info}")
        
        # 判断是否有足够的登录信息
        critical_cookies = ['web_session', 'a1', 'webId']
        has_critical = sum(1 for key in critical_cookies if key in cookies)
        
        if has_critical >= 2:
            print("✅ 包含足够的登录信息，应该能够保持登录状态")
            return True
        else:
            print("⚠️ 缺少关键登录信息，可能需要重新登录")
            return False
            
    except Exception as e:
        print(f"❌ 读取Cookies文件失败: {e}")
        return False

def test_auth_loading():
    """测试认证模块加载"""
    print("\n🔍 测试认证模块加载...")
    
    try:
        from core.auth import auth
        
        print("1. 尝试加载保存的会话...")
        result = auth.load_saved_session()
        
        if result:
            print("✅ 会话加载成功")
            
            # 检查session中的cookies
            session_cookies = dict(auth.session.cookies)
            print(f"🍪 Session中的Cookies数量: {len(session_cookies)}")
            
            # 检查是否已登录
            print("2. 检查登录状态...")
            is_logged_in = auth.is_logged_in()
            
            if is_logged_in:
                print("✅ 当前已登录状态")
                
                # 尝试获取用户信息
                user_info = auth.get_user_info()
                if user_info:
                    print(f"👤 用户信息: {user_info.get('nickname', 'N/A')}")
                else:
                    print("⚠️ 无法获取用户信息（可能是网络问题）")
            else:
                print("❌ 当前未登录状态")
                print("💡 可能原因:")
                print("   - Cookies已过期")
                print("   - 网络连接问题")
                print("   - 小红书API变化")
        else:
            print("❌ 会话加载失败")
            print("💡 可能需要重新进行Web登录")
        
        return result
        
    except Exception as e:
        print(f"❌ 认证模块测试失败: {e}")
        return False

def show_persistence_status():
    """显示持久化状态总结"""
    print("\n" + "="*50)
    print("Cookie持久化状态总结")
    print("="*50)
    
    # 检查文件
    files_to_check = [
        ("data/cookies.json", "Cookie存储文件"),
        ("data/xiaohongshu.db", "数据库文件"),
        ("data/logs", "日志目录")
    ]
    
    print("\n📁 文件状态:")
    for file_path, description in files_to_check:
        path = Path(file_path)
        if path.exists():
            if path.is_file():
                size = path.stat().st_size
                print(f"✅ {description}: 存在 ({size} 字节)")
            else:
                print(f"✅ {description}: 存在 (目录)")
        else:
            print(f"❌ {description}: 不存在")
    
    print("\n💡 使用说明:")
    print("1. 如果显示'包含足够的登录信息'且'当前已登录状态'，")
    print("   说明Cookie持久化正常工作，重启程序后会保持登录状态")
    print()
    print("2. 如果显示'当前未登录状态'，可能的原因:")
    print("   - Cookie已过期，需要重新登录")
    print("   - 网络连接问题")
    print("   - 小红书安全策略变化")
    print()
    print("3. 如果需要重新登录:")
    print("   - 运行: python main.py")
    print("   - 点击'登录'按钮")
    print("   - 在浏览器中完成登录")

def main():
    """主函数"""
    print("🔍 Cookie持久化状态检查")
    print("="*50)
    
    # 检查保存的cookies
    cookies_ok = check_saved_cookies()
    
    # 测试认证加载
    auth_ok = test_auth_loading()
    
    # 显示状态总结
    show_persistence_status()
    
    print("\n" + "="*50)
    
    if cookies_ok and auth_ok:
        print("🎉 Cookie持久化工作正常！")
        print("✅ 程序重启后应该能保持登录状态")
    elif cookies_ok:
        print("⚠️ Cookies已保存，但当前未登录状态")
        print("💡 可能需要检查网络连接或重新登录")
    else:
        print("❌ 未找到有效的登录信息")
        print("💡 请先完成Web登录")
    
    return cookies_ok or auth_ok

if __name__ == "__main__":
    success = main()
    input("\n按回车键退出...")
    sys.exit(0 if success else 1)
