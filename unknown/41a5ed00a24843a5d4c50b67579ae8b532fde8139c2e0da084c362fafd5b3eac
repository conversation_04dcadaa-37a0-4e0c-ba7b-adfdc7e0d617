"""
Cookie持久化测试脚本
"""
import sys
import os
import json
import time
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def test_cookie_manager():
    """测试Cookie管理器"""
    print("测试Cookie管理器...")
    
    try:
        from utils.crypto import cookie_manager
        
        # 模拟Web登录保存的cookies
        test_cookies = {
            'web_session': 'test_session_token_12345',
            'a1': 'test_a1_token_67890',
            'webId': 'test_web_id_abcdef',
            'userId': 'test_user_id_123',
            'xsec_token': 'test_xsec_token_456'
        }
        
        test_user_id = "test_web_user_" + str(int(time.time()))
        
        # 测试保存cookies
        print("1. 测试保存cookies...")
        cookie_manager.save_cookies(test_cookies, test_user_id)
        print("✓ Cookies保存成功")
        
        # 测试加载cookies
        print("2. 测试加载cookies...")
        loaded_cookies = cookie_manager.load_cookies()
        
        if loaded_cookies:
            print(f"✓ Cookies加载成功，共 {len(loaded_cookies)} 个")
            
            # 验证关键cookies
            key_cookies = ['web_session', 'a1', 'webId']
            missing_keys = [key for key in key_cookies if key not in loaded_cookies]
            
            if not missing_keys:
                print("✓ 关键Cookies完整")
            else:
                print(f"✗ 缺少关键Cookies: {missing_keys}")
                return False
        else:
            print("✗ Cookies加载失败")
            return False
        
        # 测试cookies有效性检查
        print("3. 测试cookies有效性...")
        is_valid = cookie_manager.is_cookies_valid()
        print(f"✓ Cookies有效性: {is_valid}")
        
        return True
        
    except Exception as e:
        print(f"✗ Cookie管理器测试失败: {e}")
        return False

def test_auth_integration():
    """测试认证模块集成"""
    print("\n测试认证模块集成...")
    
    try:
        from core.auth import auth
        from utils.crypto import cookie_manager
        
        # 首先确保有测试cookies
        test_cookies = {
            'web_session': 'integration_test_session',
            'a1': 'integration_test_a1',
            'webId': 'integration_test_webid',
            'userId': 'integration_test_userid'
        }
        
        cookie_manager.save_cookies(test_cookies, "integration_test_user")
        print("✓ 测试Cookies已保存")
        
        # 测试加载保存的会话
        print("1. 测试加载保存的会话...")
        result = auth.load_saved_session()
        
        if result:
            print("✓ 会话加载成功")
            
            # 检查session中的cookies
            session_cookies = dict(auth.session.cookies)
            print(f"✓ Session中有 {len(session_cookies)} 个cookies")
            
            # 验证关键cookies是否加载到session中
            key_cookies = ['web_session', 'a1', 'webId']
            loaded_keys = [key for key in key_cookies if key in session_cookies]
            
            if len(loaded_keys) >= 2:
                print(f"✓ 关键Cookies已加载到session: {loaded_keys}")
            else:
                print(f"⚠️ 部分关键Cookies未加载: 已加载{loaded_keys}")
        else:
            print("⚠️ 会话加载失败（可能是验证失败，这在测试环境中是正常的）")
        
        return True
        
    except Exception as e:
        print(f"✗ 认证模块集成测试失败: {e}")
        return False

def test_web_auth_integration():
    """测试Web认证集成"""
    print("\n测试Web认证集成...")
    
    try:
        from core.web_auth import web_auth
        from config.database import db
        
        # 模拟Web登录保存cookies的过程
        print("1. 模拟Web登录保存过程...")
        
        mock_cookies = {
            'web_session': 'web_auth_test_session',
            'a1': 'web_auth_test_a1',
            'webId': 'web_auth_test_webid',
            'userId': 'web_auth_test_userid',
            'xsec_token': 'web_auth_test_xsec'
        }
        
        mock_user_info = {
            'user_id': 'web_auth_test_user',
            'nickname': 'Web认证测试用户',
            'avatar': 'test_avatar_url'
        }
        
        # 调用Web认证的保存方法
        web_auth.save_login_cookies(mock_cookies, mock_user_info)
        print("✓ Web认证Cookies保存完成")
        
        # 验证是否能从主认证模块加载
        print("2. 验证主认证模块能否加载...")
        from core.auth import auth
        
        # 重新创建auth实例以清除之前的状态
        from core.auth import XiaohongshuAuth
        test_auth = XiaohongshuAuth()
        
        load_result = test_auth.load_saved_session()
        if load_result:
            print("✓ 主认证模块成功加载Web登录的Cookies")
        else:
            print("⚠️ 主认证模块加载失败（验证失败是正常的）")
        
        return True
        
    except Exception as e:
        print(f"✗ Web认证集成测试失败: {e}")
        return False

def test_persistence_across_restart():
    """测试重启后的持久化"""
    print("\n测试重启后的持久化...")
    
    try:
        # 检查数据目录
        data_dir = Path("data")
        if not data_dir.exists():
            data_dir.mkdir(exist_ok=True)
            print("✓ 数据目录已创建")
        
        # 检查cookies文件
        cookie_file = data_dir / "cookies.json"
        if cookie_file.exists():
            with open(cookie_file, 'r', encoding='utf-8') as f:
                cookie_data = json.load(f)
            
            print(f"✓ Cookies文件存在，包含数据:")
            print(f"  用户ID: {cookie_data.get('user_id', 'N/A')}")
            print(f"  Cookies数量: {len(cookie_data.get('cookies', {}))}")
            print(f"  保存时间: {time.strftime('%Y-%m-%d %H:%M:%S', time.localtime(cookie_data.get('timestamp', 0)))}")
            
            # 检查关键cookies
            cookies = cookie_data.get('cookies', {})
            key_cookies = ['web_session', 'a1', 'webId']
            present_keys = [key for key in key_cookies if key in cookies]
            
            print(f"  关键Cookies: {present_keys}")
            
            if len(present_keys) >= 2:
                print("✅ Cookies文件包含足够的登录信息，重启后应该能够使用")
            else:
                print("⚠️ Cookies文件缺少关键登录信息")
        else:
            print("ℹ️ Cookies文件不存在（需要先进行Web登录）")
        
        # 检查数据库
        try:
            from config.database import db
            db.init_database()
            
            # 尝试获取活跃会话
            session_data = db.get_active_session()
            if session_data:
                print("✓ 数据库中存在活跃会话")
                print(f"  用户ID: {session_data.get('user_id', 'N/A')}")
                print(f"  Cookies数量: {len(session_data.get('cookies', {}))}")
            else:
                print("ℹ️ 数据库中无活跃会话")
                
        except Exception as e:
            print(f"⚠️ 数据库检查失败: {e}")
        
        return True
        
    except Exception as e:
        print(f"✗ 持久化测试失败: {e}")
        return False

def show_persistence_guide():
    """显示持久化使用指南"""
    print("\n" + "="*60)
    print("Cookie持久化使用指南")
    print("="*60)
    
    print("""
🔄 Cookie持久化机制:

1. 保存位置:
   • 文件: data/cookies.json
   • 数据库: data/xiaohongshu.db (sessions表)

2. 保存时机:
   • Web登录成功后自动保存
   • 包含所有登录相关的Cookies

3. 加载时机:
   • 程序启动时自动加载
   • 检查Cookie有效性
   • 验证登录状态

4. 持久化流程:
   Web登录 → 保存Cookies → 程序重启 → 自动加载 → 验证有效性 → 恢复登录状态

✅ 如何验证持久化是否工作:

1. 完成Web登录
2. 关闭程序
3. 重新启动程序
4. 检查登录状态是否保持

📁 相关文件:
• data/cookies.json - Cookie存储文件
• data/xiaohongshu.db - 数据库文件
• data/logs/ - 日志文件

⚠️ 注意事项:
• Cookies有时效性，过期后需要重新登录
• 删除data目录会清除所有保存的登录信息
• 首次使用需要完成一次Web登录

🔧 故障排除:
• 如果重启后未保持登录状态，检查data/cookies.json文件
• 查看日志文件了解加载过程
• 可以删除data目录重新开始
""")

def main():
    """主测试函数"""
    print("🧪 Cookie持久化测试")
    print("="*50)
    
    tests = [
        ("Cookie管理器", test_cookie_manager),
        ("认证模块集成", test_auth_integration),
        ("Web认证集成", test_web_auth_integration),
        ("重启后持久化", test_persistence_across_restart)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        try:
            print(f"\n🔍 测试: {test_name}")
            if test_func():
                passed += 1
                print(f"✅ {test_name} 测试通过")
            else:
                print(f"❌ {test_name} 测试失败")
        except Exception as e:
            print(f"❌ {test_name} 测试异常: {e}")
    
    print("\n" + "="*50)
    print(f"测试完成: {passed}/{total} 项测试通过")
    
    if passed >= 3:  # 大部分测试通过
        print("🎉 Cookie持久化机制基本正常！")
        show_persistence_guide()
    else:
        print("❌ Cookie持久化存在问题，需要检查相关模块。")
    
    return passed >= 3

if __name__ == "__main__":
    success = main()
    input("\n按回车键退出...")
    sys.exit(0 if success else 1)
