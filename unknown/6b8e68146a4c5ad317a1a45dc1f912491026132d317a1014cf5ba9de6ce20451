"""
测试手动Cookie保存机制
"""
import sys
import os
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def test_cookie_save_workflow():
    """测试Cookie保存工作流程"""
    print("🧪 测试Cookie保存工作流程...")
    
    try:
        from core.web_auth import web_auth
        
        # 测试手动检查方法是否存在
        if hasattr(web_auth, 'manual_check_login'):
            print("✅ manual_check_login 方法存在")
        else:
            print("❌ manual_check_login 方法不存在")
            return False
        
        # 测试保存方法是否存在
        if hasattr(web_auth, 'save_login_cookies'):
            print("✅ save_login_cookies 方法存在")
        else:
            print("❌ save_login_cookies 方法不存在")
            return False
        
        # 测试文件保存方法是否存在
        if hasattr(web_auth, 'save_cookies_to_file'):
            print("✅ save_cookies_to_file 方法存在")
        else:
            print("❌ save_cookies_to_file 方法不存在")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

def test_manual_save_simulation():
    """模拟手动保存过程"""
    print("\n🔄 模拟手动保存过程...")
    
    try:
        from core.web_auth import web_auth
        
        # 模拟Cookie数据
        test_cookies = {
            "web_session": "manual_test_session_12345",
            "a1": "manual_test_a1_67890",
            "webId": "manual_test_webid_abcdef",
            "userId": "manual_test_userid_123",
            "xsec_token": "manual_test_xsec_456"
        }
        
        test_user_info = {
            "user_id": "manual_test_user",
            "nickname": "手动测试用户",
            "avatar": "test_avatar_url"
        }
        
        # 测试保存到文件
        print("1. 测试保存Cookie到文件...")
        web_auth.save_cookies_to_file(test_cookies, test_user_info)
        print("✅ Cookie保存到文件成功")
        
        # 测试从文件加载
        print("2. 测试从文件加载Cookie...")
        loaded_data = web_auth.load_cookies_from_file()
        
        if loaded_data:
            loaded_cookies = loaded_data.get("cookies", {})
            loaded_user_info = loaded_data.get("user_info", {})
            
            print(f"✅ Cookie加载成功")
            print(f"   用户: {loaded_user_info.get('nickname', 'Unknown')}")
            print(f"   Cookie数量: {len(loaded_cookies)}")
            
            # 验证关键Cookie
            critical_cookies = ['web_session', 'a1', 'webId']
            present_cookies = [key for key in critical_cookies if key in loaded_cookies]
            
            if len(present_cookies) >= 2:
                print(f"✅ 关键Cookie完整: {present_cookies}")
                return True
            else:
                print(f"❌ 关键Cookie不完整: {present_cookies}")
                return False
        else:
            print("❌ Cookie加载失败")
            return False
        
    except Exception as e:
        print(f"❌ 模拟测试失败: {e}")
        return False

def show_workflow_guide():
    """显示工作流程指南"""
    print("\n" + "="*60)
    print("手动Cookie保存工作流程指南")
    print("="*60)
    
    print("""
🔄 新的Cookie保存机制:

1. 自动检测阶段 (2分钟后开始)
   • 程序检测到登录状态
   • 显示提示: "检测到登录成功！请点击'检查状态'按钮保存Cookie"
   • 不自动保存Cookie，等待用户操作

2. 用户确认阶段
   • 用户确认已完成登录
   • 确认页面已跳转到正确位置
   • 点击"检查状态"按钮

3. 手动保存阶段
   • 程序重新获取当前所有Cookie
   • 验证登录状态
   • 保存Cookie到data/cookie.txt
   • 显示保存成功信息
   • 5秒后自动关闭浏览器

🎯 优势:
• 用户完全控制何时保存Cookie
• 确保保存的是用户确认的正确状态
• 避免保存中间状态或无效Cookie
• 提供详细的保存信息反馈

📋 使用步骤:
1. python main.py
2. 点击"登录"
3. 在浏览器中完成登录
4. 等待程序提示"检测到登录成功"
5. 确认登录状态正确
6. 点击"检查状态"按钮
7. 确认Cookie保存成功

⚠️ 注意事项:
• 只有点击"检查状态"才会保存Cookie
• 确保在正确的登录状态下点击
• 保存后浏览器会自动关闭
• Cookie保存在data/cookie.txt文件中

🔧 故障排除:
• 如果检测失败，确认已完全登录
• 如果保存失败，检查文件权限
• 如果Cookie无效，重新登录并保存
""")

def cleanup_test_files():
    """清理测试文件"""
    try:
        test_file = "data/cookie.txt"
        if os.path.exists(test_file):
            # 备份现有文件
            import shutil
            backup_file = "data/cookie_backup_before_test.txt"
            shutil.copy2(test_file, backup_file)
            print(f"📁 现有Cookie文件已备份到: {backup_file}")
    except Exception as e:
        print(f"⚠️ 备份文件失败: {e}")

def main():
    """主测试函数"""
    print("🧪 手动Cookie保存机制测试")
    print("="*50)
    
    # 备份现有文件
    cleanup_test_files()
    
    tests = [
        ("Cookie保存工作流程", test_cookie_save_workflow),
        ("手动保存模拟", test_manual_save_simulation)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        try:
            if test_func():
                passed += 1
                print(f"✅ {test_name} 测试通过")
            else:
                print(f"❌ {test_name} 测试失败")
        except Exception as e:
            print(f"❌ {test_name} 测试异常: {e}")
    
    print("\n" + "="*50)
    print(f"测试完成: {passed}/{total} 项测试通过")
    
    if passed == total:
        print("🎉 手动Cookie保存机制实现完成！")
        show_workflow_guide()
    else:
        print("❌ 部分测试失败，需要检查实现。")
    
    return passed == total

if __name__ == "__main__":
    success = main()
    input("\n按回车键退出...")
    sys.exit(0 if success else 1)
