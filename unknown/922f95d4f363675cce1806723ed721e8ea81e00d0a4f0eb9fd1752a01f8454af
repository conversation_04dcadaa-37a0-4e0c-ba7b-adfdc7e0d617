"""
测试任务创建窗口修复
"""
import sys
import os
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def test_task_creator_import():
    """测试任务创建器导入"""
    print("🔍 测试任务创建器导入...")
    
    try:
        from gui.task_creator import TaskCreatorWindow
        print("✅ TaskCreatorWindow 导入成功")
        return True
    except Exception as e:
        print(f"❌ TaskCreatorWindow 导入失败: {e}")
        return False

def test_customtkinter_textbox():
    """测试CustomTkinter Textbox组件"""
    print("\n🔍 测试CustomTkinter Textbox组件...")
    
    try:
        import customtkinter as ctk
        import tkinter as tk
        
        # 创建测试窗口
        root = tk.Tk()
        root.withdraw()  # 隐藏窗口
        
        # 测试CTkTextbox创建
        textbox = ctk.CTkTextbox(root, height=60)
        print("✅ CTkTextbox 创建成功")
        
        # 测试插入文本
        textbox.insert("1.0", "测试文本")
        print("✅ 文本插入成功")
        
        # 测试获取文本
        text = textbox.get("1.0", "end-1c")
        if text == "测试文本":
            print("✅ 文本获取成功")
        else:
            print(f"⚠️ 文本获取异常: {text}")
        
        # 测试配置颜色
        textbox.configure(text_color="gray")
        print("✅ 颜色配置成功")
        
        root.destroy()
        return True
        
    except Exception as e:
        print(f"❌ CTkTextbox 测试失败: {e}")
        return False

def test_task_creator_creation():
    """测试任务创建器窗口创建"""
    print("\n🔍 测试任务创建器窗口创建...")
    
    try:
        import tkinter as tk
        import customtkinter as ctk
        from gui.task_creator import TaskCreatorWindow
        
        # 创建主窗口
        root = tk.Tk()
        root.withdraw()  # 隐藏主窗口
        
        # 尝试创建任务创建器窗口
        print("创建任务创建器窗口...")
        task_creator = TaskCreatorWindow(root)
        
        print("✅ 任务创建器窗口创建成功")
        
        # 测试窗口是否存在
        if task_creator.window.winfo_exists():
            print("✅ 窗口存在且可访问")
        else:
            print("❌ 窗口不存在")
            return False
        
        # 关闭窗口
        task_creator.window.destroy()
        root.destroy()
        
        return True
        
    except Exception as e:
        print(f"❌ 任务创建器窗口创建失败: {e}")
        return False

def test_placeholder_text_handling():
    """测试提示文本处理"""
    print("\n🔍 测试提示文本处理...")
    
    try:
        import tkinter as tk
        import customtkinter as ctk
        
        # 创建测试窗口
        root = tk.Tk()
        root.withdraw()
        
        # 创建测试textbox
        textbox = ctk.CTkTextbox(root, height=60)
        
        # 插入提示文本
        placeholder_text = "输入任务描述（可选）"
        textbox.insert("1.0", placeholder_text)
        textbox.configure(text_color="gray")
        print("✅ 提示文本插入成功")
        
        # 测试获取文本
        current_text = textbox.get("1.0", "end-1c")
        if current_text == placeholder_text:
            print("✅ 提示文本获取正确")
        else:
            print(f"⚠️ 提示文本获取异常: {current_text}")
        
        # 测试清除提示文本
        textbox.delete("1.0", "end")
        textbox.configure(text_color="white")
        print("✅ 提示文本清除成功")
        
        # 测试插入用户文本
        user_text = "用户输入的内容"
        textbox.insert("1.0", user_text)
        
        current_text = textbox.get("1.0", "end-1c")
        if current_text == user_text:
            print("✅ 用户文本处理正确")
        else:
            print(f"⚠️ 用户文本处理异常: {current_text}")
        
        root.destroy()
        return True
        
    except Exception as e:
        print(f"❌ 提示文本处理测试失败: {e}")
        return False

def show_fix_summary():
    """显示修复总结"""
    print("\n" + "="*60)
    print("任务创建窗口修复总结")
    print("="*60)
    
    print("""
🔧 修复的问题:

1. CTkTextbox placeholder_text 参数错误
   • 问题: CTkTextbox 不支持 placeholder_text 参数
   • 修复: 移除 placeholder_text，使用手动插入文本方式

2. 提示文本实现
   • 插入提示文本到 textbox
   • 设置灰色文本颜色
   • 绑定焦点事件处理

3. 焦点事件处理
   • FocusIn: 清除提示文本，设置正常颜色
   • FocusOut: 如果为空，恢复提示文本

4. 数据获取优化
   • 过滤提示文本，避免误判
   • 确保获取真实用户输入

✅ 修复后的功能:
• 任务描述输入框正常工作
• 自定义回复输入框正常工作
• 提示文本正确显示和隐藏
• 用户输入正确处理

🎯 现在可以正常使用:
1. python main.py
2. 点击"登录"完成登录
3. 点击"创建任务"
4. 填写任务信息
5. 创建任务成功

⚠️ 注意事项:
• 提示文本会在获得焦点时自动清除
• 失去焦点且为空时会恢复提示文本
• 任务配置会正确过滤提示文本
""")

def main():
    """主测试函数"""
    print("🧪 任务创建窗口修复测试")
    print("="*50)
    
    tests = [
        ("任务创建器导入", test_task_creator_import),
        ("CTkTextbox组件", test_customtkinter_textbox),
        ("任务创建器窗口创建", test_task_creator_creation),
        ("提示文本处理", test_placeholder_text_handling)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        try:
            if test_func():
                passed += 1
                print(f"✅ {test_name} 测试通过")
            else:
                print(f"❌ {test_name} 测试失败")
        except Exception as e:
            print(f"❌ {test_name} 测试异常: {e}")
    
    print("\n" + "="*50)
    print(f"测试完成: {passed}/{total} 项测试通过")
    
    if passed == total:
        print("🎉 任务创建窗口修复完成！")
        show_fix_summary()
    else:
        print("❌ 部分测试失败，需要进一步检查。")
    
    return passed == total

if __name__ == "__main__":
    success = main()
    input("\n按回车键退出...")
    sys.exit(0 if success else 1)
