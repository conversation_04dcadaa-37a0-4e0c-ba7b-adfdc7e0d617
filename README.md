# 小红书自动回帖机器人

一个功能完善的小红书自动搜索关键词并自动回帖的可视化软件，支持多任务、多关键词管理。

## 功能特性

### 🔐 用户认证
- **Web浏览器登录**：在浏览器中手动登录，自动获取Cookie
- 支持所有官方登录方式（手机验证码、邮箱、扫码等）
- 自动保存登录状态
- 支持会话持久化

### 🔍 智能搜索
- 多关键词搜索管理
- 支持搜索结果排序
- 自动记录搜索历史
- 搜索推荐功能

### 🤖 自动回复
- 智能回复内容生成
- 可自定义回复模板
- 支持批量回复
- 回复频率控制

### 📋 任务管理
- 多任务并发执行
- 任务状态实时监控
- 支持任务暂停/恢复
- 详细的任务统计

### 📊 数据监控
- 实时运行状态监控
- 详细的操作日志
- 性能指标统计
- 数据可视化展示

### 🎨 现代化界面
- 基于CustomTkinter的现代化GUI
- 深色主题支持
- 响应式布局设计
- 用户友好的操作体验

## 安装说明

### 环境要求
- Python 3.8+
- Windows 10/11 (推荐)
- Chrome浏览器（用于Web登录）

### 安装步骤

1. **克隆项目**
   ```bash
   git clone <repository-url>
   cd xiaohongshu_auto_reply_robot
   ```

2. **安装依赖**
   ```bash
   pip install -r requirements.txt
   ```

3. **运行程序**
   ```bash
   python main.py
   ```
   
   或者双击运行 `run.bat` 文件

## 使用指南

### 1. 首次登录
1. 启动程序后，点击"登录"按钮
2. 在自动打开的浏览器中完成登录：
   - 手机号码 + 验证码
   - 邮箱 + 密码
   - 扫描二维码
   - 第三方账号登录
3. 等待程序自动检测并保存Cookie

### 2. 管理关键词
1. 点击"关键词管理"按钮
2. 添加需要搜索的关键词
3. 可以批量导入关键词
4. 支持启用/禁用关键词

### 3. 创建任务
1. 点击"创建任务"按钮
2. 选择要使用的关键词
3. 配置任务参数
4. 启动任务开始自动回复

### 4. 监控任务
1. 在"任务监控"窗口查看运行状态
2. 实时查看日志信息
3. 监控性能指标
4. 控制任务的启动/停止

## 配置说明

### 基本配置
程序会自动创建 `data/config.json` 配置文件，包含以下主要配置：

```json
{
  "task": {
    "max_concurrent_tasks": 5,
    "search_interval": 30,
    "reply_interval": 60,
    "max_replies_per_note": 1,
    "max_daily_replies": 100
  },
  "reply": {
    "templates": [
      "很棒的分享！👍",
      "学到了，感谢分享！",
      "这个很实用，收藏了！"
    ]
  }
}
```

### 回复模板
可以在配置文件中自定义回复模板，支持以下占位符：
- `{title}` - 笔记标题
- `{content}` - 笔记内容

## 目录结构

```
xiaohongshu_auto_reply_robot/
├── main.py                 # 主程序入口
├── requirements.txt        # 依赖包列表
├── run.bat                # Windows启动脚本
├── README.md              # 说明文档
├── config/                # 配置模块
│   ├── settings.py        # 配置管理
│   └── database.py        # 数据库操作
├── core/                  # 核心功能模块
│   ├── auth.py           # 用户认证
│   ├── search.py         # 搜索引擎
│   ├── reply.py          # 自动回复
│   └── task_manager.py   # 任务管理
├── gui/                   # 图形界面模块
│   ├── main_window.py    # 主窗口
│   ├── login_window.py   # 登录窗口
│   ├── keyword_manager.py # 关键词管理
│   └── task_monitor.py   # 任务监控
├── utils/                 # 工具模块
│   ├── logger.py         # 日志工具
│   ├── crypto.py         # 加密工具
│   └── helpers.py        # 辅助函数
└── data/                  # 数据目录
    ├── config.json       # 配置文件
    ├── xiaohongshu.db    # 数据库文件
    ├── cookies.json      # Cookie存储
    └── logs/             # 日志文件
```

## 注意事项

### ⚠️ 重要提醒
1. **浏览器要求**: 需要安装Chrome浏览器用于登录
2. **登录时间**: 前2分钟为等待时间，2分钟后开始检测登录状态
3. **合规使用**: 请遵守小红书平台规则，避免过度频繁的操作
4. **账号安全**: 建议使用小号进行测试，避免主账号被限制
5. **内容质量**: 回复内容应该有意义，避免垃圾信息
6. **频率控制**: 合理设置回复间隔，避免被平台检测

### 🔧 接入真实API
要使用真实的小红书功能，需要：

1. **获取真实API接口**
   - 小红书登录API
   - 搜索API
   - 评论API

2. **修改认证模块** (`core/auth.py`)
   ```python
   # 替换演示代码为真实API调用
   def generate_qr_code(self):
       # 调用真实的小红书登录API
       response = requests.get("真实的二维码API")
       # 处理真实的响应数据
   ```

3. **更新搜索模块** (`core/search.py`)
   ```python
   # 使用真实的搜索API
   def search_notes(self, keyword):
       # 调用真实的搜索API
       response = requests.post("真实的搜索API", data=search_data)
   ```

4. **配置请求签名**
   - 获取真实的签名算法
   - 更新 `utils/crypto.py` 中的签名方法

### 🔒 隐私保护
- 所有登录信息仅保存在本地
- 不会上传任何个人数据
- Cookie信息经过加密存储

### 🐛 问题反馈
如果遇到问题，请：
1. 查看日志文件 `data/logs/`
2. 检查网络连接
3. 确认账号登录状态
4. 提交Issue描述问题

## 技术架构

### 核心技术栈
- **GUI框架**: CustomTkinter (现代化界面)
- **HTTP请求**: requests (网络通信)
- **数据库**: SQLite (本地存储)
- **多任务**: threading (并发处理)
- **加密**: cryptography (数据安全)

### 设计模式
- **MVC架构**: 分离界面、逻辑和数据
- **观察者模式**: 任务状态通知
- **单例模式**: 全局配置管理
- **工厂模式**: 组件创建管理

## 更新日志

### v1.0.0 (2025-01-31)
- ✨ 初始版本发布
- 🔐 实现扫码登录功能
- 🔍 完成搜索引擎模块
- 🤖 实现自动回复功能
- 📋 添加任务管理系统
- 🎨 完成现代化GUI界面

## 许可证

本项目仅供学习和研究使用，请勿用于商业用途。

## 免责声明

本软件仅供技术学习和研究使用。使用本软件所产生的任何后果，包括但不限于账号被限制、数据丢失等，均由使用者自行承担。开发者不承担任何责任。

请在使用前仔细阅读小红书的用户协议和社区规范，确保合规使用。
