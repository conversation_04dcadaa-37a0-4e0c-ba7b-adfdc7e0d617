"""
日志工具模块
"""
import logging
import os
from datetime import datetime
from typing import Optional

class Logger:
    def __init__(self, name: str = "XiaohongshuBot", log_dir: str = "data/logs"):
        self.name = name
        self.log_dir = log_dir
        self.ensure_log_dir()
        self.setup_logger()
    
    def ensure_log_dir(self):
        """确保日志目录存在"""
        if not os.path.exists(self.log_dir):
            os.makedirs(self.log_dir)
    
    def setup_logger(self):
        """设置日志器"""
        self.logger = logging.getLogger(self.name)
        self.logger.setLevel(logging.DEBUG)
        
        # 避免重复添加处理器
        if self.logger.handlers:
            return
        
        # 创建格式器
        formatter = logging.Formatter(
            '%(asctime)s - %(name)s - %(levelname)s - %(message)s',
            datefmt='%Y-%m-%d %H:%M:%S'
        )
        
        # 控制台处理器
        console_handler = logging.StreamHandler()
        console_handler.setLevel(logging.INFO)
        console_handler.setFormatter(formatter)
        self.logger.addHandler(console_handler)
        
        # 文件处理器 - 所有日志
        today = datetime.now().strftime('%Y-%m-%d')
        all_log_file = os.path.join(self.log_dir, f"all_{today}.log")
        file_handler = logging.FileHandler(all_log_file, encoding='utf-8')
        file_handler.setLevel(logging.DEBUG)
        file_handler.setFormatter(formatter)
        self.logger.addHandler(file_handler)
        
        # 文件处理器 - 错误日志
        error_log_file = os.path.join(self.log_dir, f"error_{today}.log")
        error_handler = logging.FileHandler(error_log_file, encoding='utf-8')
        error_handler.setLevel(logging.ERROR)
        error_handler.setFormatter(formatter)
        self.logger.addHandler(error_handler)
    
    def debug(self, message: str, extra: Optional[dict] = None):
        """调试日志"""
        self.logger.debug(message, extra=extra)
    
    def info(self, message: str, extra: Optional[dict] = None):
        """信息日志"""
        self.logger.info(message, extra=extra)
    
    def warning(self, message: str, extra: Optional[dict] = None):
        """警告日志"""
        self.logger.warning(message, extra=extra)
    
    def error(self, message: str, extra: Optional[dict] = None):
        """错误日志"""
        self.logger.error(message, extra=extra)
    
    def critical(self, message: str, extra: Optional[dict] = None):
        """严重错误日志"""
        self.logger.critical(message, extra=extra)
    
    def log_api_request(self, method: str, url: str, status_code: int, response_time: float):
        """记录API请求日志"""
        self.info(f"API请求: {method} {url} - 状态码: {status_code} - 响应时间: {response_time:.2f}s")
    
    def log_task_start(self, task_name: str, keywords: list):
        """记录任务开始日志"""
        self.info(f"任务开始: {task_name} - 关键词: {', '.join(keywords)}")
    
    def log_task_end(self, task_name: str, success_count: int, error_count: int):
        """记录任务结束日志"""
        self.info(f"任务结束: {task_name} - 成功: {success_count} - 失败: {error_count}")
    
    def log_search_result(self, keyword: str, note_count: int):
        """记录搜索结果日志"""
        self.info(f"搜索结果: 关键词 '{keyword}' 找到 {note_count} 条笔记")
    
    def log_reply_attempt(self, note_id: str, reply_content: str):
        """记录回复尝试日志"""
        self.info(f"尝试回复笔记: {note_id} - 内容: {reply_content[:50]}...")
    
    def log_reply_success(self, note_id: str):
        """记录回复成功日志"""
        self.info(f"回复成功: 笔记 {note_id}")
    
    def log_reply_error(self, note_id: str, error: str):
        """记录回复失败日志"""
        self.error(f"回复失败: 笔记 {note_id} - 错误: {error}")
    
    def log_login_attempt(self, method: str):
        """记录登录尝试日志"""
        self.info(f"尝试登录: {method}")
    
    def log_login_success(self, user_id: str):
        """记录登录成功日志"""
        self.info(f"登录成功: 用户ID {user_id}")
    
    def log_login_error(self, error: str):
        """记录登录失败日志"""
        self.error(f"登录失败: {error}")

# 全局日志实例
logger = Logger()
