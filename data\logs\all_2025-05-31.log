2025-05-31 11:20:39 - <PERSON><PERSON>shuBot - INFO - 已加载 0 个任务
2025-05-31 11:22:35 - <PERSON><PERSON>shuBot - INFO - 已加载 0 个任务
2025-05-31 11:22:35 - <PERSON><PERSON><PERSON>Bot - INFO - 数据库初始化完成
2025-05-31 11:22:35 - <PERSON><PERSON><PERSON>Bot - INFO - 小红书自动回帖机器人 v1.0.0 启动
2025-05-31 11:22:35 - <PERSON><PERSON>shuBot - INFO - Python版本: 3.12.7 | packaged by Anaconda, Inc. | (main, Oct  4 2024, 13:17:27) [MSC v.1929 64 bit (AMD64)]
2025-05-31 11:22:35 - <PERSON><PERSON>shuBot - INFO - 工作目录: D:\AI\pc_tool\xiaohongshu_auto_reply_robot
2025-05-31 11:22:42 - <PERSON><PERSON>shuBot - INFO - 登录状态: 正在生成二维码...
2025-05-31 11:22:42 - <PERSON><PERSON>shuBot - ERROR - 登录失败: 二维码生成失败: 404
2025-05-31 11:22:42 - <PERSON><PERSON>shuBot - INFO - 登录状态: 二维码生成失败，请重试
2025-05-31 11:23:28 - <PERSON><PERSON>shuBot - INFO - 已停止所有任务
2025-05-31 11:23:28 - <PERSON>hongshuBot - INFO - 程序正常退出
2025-05-31 11:26:17 - <PERSON>hongshuBot - INFO - 已加载 0 个任务
2025-05-31 11:26:17 - <PERSON>hongshuBot - INFO - 数据库初始化完成
2025-05-31 11:26:17 - <PERSON>hongshuBot - INFO - 小红书自动回帖机器人 v1.0.0 启动
2025-05-31 11:26:17 - XiaohongshuBot - INFO - Python版本: 3.12.7 | packaged by Anaconda, Inc. | (main, Oct  4 2024, 13:17:27) [MSC v.1929 64 bit (AMD64)]
2025-05-31 11:26:17 - XiaohongshuBot - INFO - 工作目录: D:\AI\pc_tool\xiaohongshu_auto_reply_robot
2025-05-31 11:26:20 - XiaohongshuBot - INFO - 登录状态: 正在生成二维码...
2025-05-31 11:26:20 - XiaohongshuBot - INFO - 尝试登录: 演示二维码生成
2025-05-31 11:26:20 - XiaohongshuBot - INFO - 登录状态: 请使用小红书APP扫描二维码
2025-05-31 11:26:20 - XiaohongshuBot - INFO - 登录状态: 等待扫描二维码...
2025-05-31 11:26:22 - XiaohongshuBot - INFO - 登录状态: 等待扫描二维码...
2025-05-31 11:26:24 - XiaohongshuBot - INFO - 登录状态: 等待扫描二维码...
2025-05-31 11:26:26 - XiaohongshuBot - INFO - 登录状态: 等待扫描二维码...
2025-05-31 11:26:28 - XiaohongshuBot - INFO - 登录状态: 等待扫描二维码...
2025-05-31 11:26:30 - XiaohongshuBot - INFO - 登录状态: 已扫描，请在手机上确认登录
2025-05-31 11:26:32 - XiaohongshuBot - INFO - 登录状态: 已扫描，请在手机上确认登录
2025-05-31 11:26:34 - XiaohongshuBot - INFO - 登录状态: 已扫描，请在手机上确认登录
2025-05-31 11:26:36 - XiaohongshuBot - INFO - 登录状态: 已扫描，请在手机上确认登录
2025-05-31 11:26:38 - XiaohongshuBot - INFO - 登录状态: 已扫描，请在手机上确认登录
2025-05-31 11:26:40 - XiaohongshuBot - INFO - 登录状态: 已扫描，请在手机上确认登录
2025-05-31 11:26:42 - XiaohongshuBot - INFO - 登录状态: 已扫描，请在手机上确认登录
2025-05-31 11:26:44 - XiaohongshuBot - INFO - 登录状态: 已扫描，请在手机上确认登录
2025-05-31 11:26:46 - XiaohongshuBot - INFO - 登录状态: 已扫描，请在手机上确认登录
2025-05-31 11:26:48 - XiaohongshuBot - INFO - 登录状态: 已扫描，请在手机上确认登录
2025-05-31 11:26:50 - XiaohongshuBot - INFO - 登录状态: 已扫描，请在手机上确认登录
2025-05-31 11:26:52 - XiaohongshuBot - INFO - 登录状态: 已扫描，请在手机上确认登录
2025-05-31 11:26:54 - XiaohongshuBot - INFO - 登录状态: 已扫描，请在手机上确认登录
2025-05-31 11:26:56 - XiaohongshuBot - INFO - 登录状态: 已扫描，请在手机上确认登录
2025-05-31 11:27:05 - XiaohongshuBot - INFO - 用户中断程序
2025-05-31 11:27:08 - XiaohongshuBot - INFO - 已加载 0 个任务
2025-05-31 11:27:08 - XiaohongshuBot - INFO - 数据库初始化完成
2025-05-31 11:27:08 - XiaohongshuBot - INFO - 小红书自动回帖机器人 v1.0.0 启动
2025-05-31 11:27:08 - XiaohongshuBot - INFO - Python版本: 3.12.7 | packaged by Anaconda, Inc. | (main, Oct  4 2024, 13:17:27) [MSC v.1929 64 bit (AMD64)]
2025-05-31 11:27:08 - XiaohongshuBot - INFO - 工作目录: D:\AI\pc_tool\xiaohongshu_auto_reply_robot
2025-05-31 11:27:11 - XiaohongshuBot - INFO - 登录状态: 正在生成二维码...
2025-05-31 11:27:11 - XiaohongshuBot - INFO - 尝试登录: 演示二维码生成
2025-05-31 11:27:11 - XiaohongshuBot - INFO - 登录状态: 请使用小红书APP扫描二维码
2025-05-31 11:27:11 - XiaohongshuBot - INFO - 登录状态: 等待扫描二维码...
2025-05-31 11:27:13 - XiaohongshuBot - INFO - 登录状态: 等待扫描二维码...
2025-05-31 11:27:15 - XiaohongshuBot - INFO - 登录状态: 等待扫描二维码...
2025-05-31 11:27:17 - XiaohongshuBot - INFO - 登录状态: 等待扫描二维码...
2025-05-31 11:27:19 - XiaohongshuBot - INFO - 登录状态: 等待扫描二维码...
2025-05-31 11:27:21 - XiaohongshuBot - INFO - 登录状态: 已扫描，请在手机上确认登录
2025-05-31 11:27:23 - XiaohongshuBot - INFO - 登录状态: 已扫描，请在手机上确认登录
2025-05-31 11:27:25 - XiaohongshuBot - INFO - 登录状态: 已扫描，请在手机上确认登录
2025-05-31 11:27:27 - XiaohongshuBot - INFO - 登录状态: 已扫描，请在手机上确认登录
2025-05-31 11:27:29 - XiaohongshuBot - INFO - 登录状态: 已扫描，请在手机上确认登录
2025-05-31 11:27:31 - XiaohongshuBot - INFO - 用户中断程序
