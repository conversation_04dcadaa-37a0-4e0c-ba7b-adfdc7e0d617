"""
小红书自动回帖机器人 - 功能演示
"""
import sys
import os
import time
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def demo_config():
    """演示配置功能"""
    print("=" * 50)
    print("配置管理演示")
    print("=" * 50)
    
    from config.settings import settings
    
    print("当前配置信息:")
    print(f"应用名称: {settings.get('app.name')}")
    print(f"应用版本: {settings.get('app.version')}")
    print(f"最大并发任务: {settings.get('task.max_concurrent_tasks')}")
    print(f"搜索间隔: {settings.get('task.search_interval')}秒")
    print(f"回复间隔: {settings.get('task.reply_interval')}秒")
    print(f"每日最大回复数: {settings.get('task.max_daily_replies')}")
    
    print("\n回复模板:")
    templates = settings.get('reply.templates', [])
    for i, template in enumerate(templates, 1):
        print(f"  {i}. {template}")

def demo_database():
    """演示数据库功能"""
    print("\n" + "=" * 50)
    print("数据库管理演示")
    print("=" * 50)
    
    from config.database import db
    
    # 初始化数据库
    print("初始化数据库...")
    db.init_database()
    print("✓ 数据库初始化完成")
    
    # 添加示例关键词
    demo_keywords = [
        "摄影技巧",
        "美食推荐", 
        "旅行攻略",
        "护肤心得",
        "穿搭分享"
    ]
    
    print("\n添加示例关键词:")
    for keyword in demo_keywords:
        if db.add_keyword(keyword):
            print(f"✓ 添加关键词: {keyword}")
        else:
            print(f"- 关键词已存在: {keyword}")
    
    # 获取关键词列表
    keywords = db.get_keywords()
    print(f"\n当前关键词总数: {len(keywords)}")
    
    print("关键词列表:")
    for kw in keywords[:5]:  # 只显示前5个
        print(f"  - {kw.get('keyword')} (ID: {kw.get('id')}, 启用: {kw.get('enabled')})")

def demo_crypto():
    """演示加密功能"""
    print("\n" + "=" * 50)
    print("加密工具演示")
    print("=" * 50)
    
    from utils.crypto import crypto
    
    # 演示签名生成
    print("生成请求签名:")
    test_url = "https://edith.xiaohongshu.com/api/sns/web/v1/search/notes"
    test_data = {"keyword": "测试", "page": 1}
    
    headers = crypto.sign_request("POST", test_url, test_data)
    
    print("生成的签名头:")
    for key, value in headers.items():
        if len(value) > 50:
            print(f"  {key}: {value[:50]}...")
        else:
            print(f"  {key}: {value}")
    
    # 演示设备指纹生成
    print("\n生成设备指纹:")
    fingerprint = crypto.generate_device_fingerprint()
    for key, value in fingerprint.items():
        print(f"  {key}: {value}")

def demo_helpers():
    """演示辅助函数"""
    print("\n" + "=" * 50)
    print("辅助函数演示")
    print("=" * 50)
    
    from utils.helpers import (
        is_valid_keyword, clean_text, generate_reply_content,
        format_time_ago, parse_xiaohongshu_time
    )
    from datetime import datetime, timedelta
    
    # 关键词验证
    print("关键词验证:")
    test_keywords = ["正常关键词", "", "很长很长很长很长很长很长很长很长很长很长的关键词", "包含<script>的关键词"]
    for kw in test_keywords:
        valid = is_valid_keyword(kw)
        print(f"  '{kw[:20]}...' -> {'有效' if valid else '无效'}")
    
    # 文本清理
    print("\n文本清理:")
    dirty_text = "  <p>这是一个包含HTML标签的文本</p>  \n\n  "
    clean = clean_text(dirty_text)
    print(f"  原文: '{dirty_text}'")
    print(f"  清理后: '{clean}'")
    
    # 回复生成
    print("\n回复内容生成:")
    templates = ["很棒的分享！👍", "学到了，感谢分享！", "这个很实用，收藏了！"]
    for i in range(3):
        reply = generate_reply_content(templates, "测试标题", "测试内容")
        print(f"  生成回复 {i+1}: {reply}")
    
    # 时间格式化
    print("\n时间格式化:")
    test_times = [
        datetime.now(),
        datetime.now() - timedelta(hours=2),
        datetime.now() - timedelta(days=1),
        datetime.now() - timedelta(days=3)
    ]
    for t in test_times:
        formatted = format_time_ago(t)
        print(f"  {t.strftime('%Y-%m-%d %H:%M:%S')} -> {formatted}")

def demo_logger():
    """演示日志功能"""
    print("\n" + "=" * 50)
    print("日志系统演示")
    print("=" * 50)
    
    from utils.logger import logger
    
    print("记录不同级别的日志:")
    logger.debug("这是一条调试信息")
    logger.info("程序启动成功")
    logger.warning("这是一条警告信息")
    logger.error("这是一条错误信息")
    
    print("\n记录特定类型的日志:")
    logger.log_api_request("POST", "https://example.com/api", 200, 0.5)
    logger.log_search_result("测试关键词", 10)
    logger.log_reply_success("test_note_id")
    logger.log_login_success("test_user_id")
    
    print("✓ 日志已记录到 data/logs/ 目录")

def demo_task_simulation():
    """演示任务模拟"""
    print("\n" + "=" * 50)
    print("任务管理演示")
    print("=" * 50)
    
    from core.task_manager import task_manager
    
    print("创建示例任务:")
    
    # 创建任务
    task_config = {
        "search_interval": 10,
        "reply_interval": 30,
        "max_replies_per_note": 1,
        "min_like_count": 0,
        "max_like_count": 1000
    }
    
    task_id = task_manager.create_task(
        name="演示任务",
        keywords=["摄影技巧", "美食推荐"],
        config=task_config
    )
    
    if task_id:
        print(f"✓ 创建任务成功，ID: {task_id}")
        
        # 获取任务列表
        tasks = task_manager.get_task_list()
        print(f"当前任务总数: {len(tasks)}")
        
        for task in tasks:
            print(f"  任务: {task.get('name')} - 状态: {task.get('status')}")
    else:
        print("✗ 创建任务失败")

def main():
    """主演示函数"""
    print("🎉 小红书自动回帖机器人 - 功能演示")
    print("本演示将展示程序的各个核心功能模块")
    print("\n按回车键开始演示...")
    input()
    
    try:
        # 配置管理演示
        demo_config()
        input("\n按回车键继续...")
        
        # 数据库功能演示
        demo_database()
        input("\n按回车键继续...")
        
        # 加密工具演示
        demo_crypto()
        input("\n按回车键继续...")
        
        # 辅助函数演示
        demo_helpers()
        input("\n按回车键继续...")
        
        # 日志系统演示
        demo_logger()
        input("\n按回车键继续...")
        
        # 任务管理演示
        demo_task_simulation()
        
        print("\n" + "=" * 50)
        print("🎉 演示完成！")
        print("=" * 50)
        print("\n主要功能模块都已正常工作。")
        print("现在可以运行主程序体验完整功能:")
        print("  python main.py")
        print("\n注意: 实际使用时需要先登录小红书账号。")
        
    except Exception as e:
        print(f"\n❌ 演示过程中发生错误: {e}")
        import traceback
        traceback.print_exc()
    
    input("\n按回车键退出...")

if __name__ == "__main__":
    main()
