"""
Web浏览器登录窗口
"""
import tkinter as tk
from tkinter import messagebox, scrolledtext
import customtkinter as ctk
import threading

from core.web_auth import web_auth
from utils.logger import logger

class WebLoginWindow:
    """Web浏览器登录窗口类"""
    
    def __init__(self, parent, success_callback=None):
        self.parent = parent
        self.success_callback = success_callback
        
        # 创建窗口
        self.window = ctk.CTkToplevel(parent)
        self.setup_window()
        self.setup_ui()
        
        # 设置回调
        web_auth.set_login_callback(self.on_login_status_change)
        
    def setup_window(self):
        """设置窗口属性"""
        self.window.title("Web浏览器登录")
        self.window.geometry("500x600")
        self.window.resizable(True, True)
        
        # 设置为模态窗口
        self.window.transient(self.parent)
        self.window.grab_set()
        
        # 居中显示
        self.center_window()
        
        # 设置关闭事件
        self.window.protocol("WM_DELETE_WINDOW", self.on_closing)
    
    def center_window(self):
        """窗口居中"""
        self.window.update_idletasks()
        x = (self.window.winfo_screenwidth() // 2) - (500 // 2)
        y = (self.window.winfo_screenheight() // 2) - (600 // 2)
        self.window.geometry(f"500x600+{x}+{y}")
    
    def setup_ui(self):
        """设置用户界面"""
        # 主框架
        main_frame = ctk.CTkFrame(self.window)
        main_frame.pack(fill="both", expand=True, padx=20, pady=20)
        
        # 标题
        title_label = ctk.CTkLabel(
            main_frame,
            text="Web浏览器登录",
            font=ctk.CTkFont(size=20, weight="bold")
        )
        title_label.pack(pady=10)
        
        # 说明文本
        self.create_instructions(main_frame)
        
        # 状态显示
        self.create_status_section(main_frame)
        
        # 按钮区域
        self.create_buttons(main_frame)
        
        # 日志区域
        self.create_log_section(main_frame)
    
    def create_instructions(self, parent):
        """创建说明文本"""
        instructions_frame = ctk.CTkFrame(parent)
        instructions_frame.pack(fill="x", pady=10)
        
        instructions_label = ctk.CTkLabel(
            instructions_frame,
            text="登录说明:",
            font=ctk.CTkFont(size=14, weight="bold")
        )
        instructions_label.pack(anchor="w", padx=10, pady=5)
        
        instructions_text = """1. 点击"开始登录"按钮启动浏览器
2. 浏览器会自动打开小红书官网
3. 在浏览器中手动完成登录操作
4. 登录成功后程序会自动保存Cookie
5. 浏览器会自动关闭，登录完成

⏰ 时间充足：
• 最长等待时间：30分钟
• 无需着急，慢慢操作即可
• 程序会实时监控登录状态

支持的登录方式：
• 手机号码 + 验证码
• 邮箱 + 密码
• 扫描二维码登录
• 第三方账号登录"""
        
        instructions_display = ctk.CTkTextbox(
            instructions_frame,
            height=120,
            font=ctk.CTkFont(size=12)
        )
        instructions_display.pack(fill="x", padx=10, pady=5)
        instructions_display.insert("1.0", instructions_text)
        instructions_display.configure(state="disabled")
    
    def create_status_section(self, parent):
        """创建状态显示区域"""
        status_frame = ctk.CTkFrame(parent)
        status_frame.pack(fill="x", pady=10)
        
        status_title = ctk.CTkLabel(
            status_frame,
            text="登录状态:",
            font=ctk.CTkFont(size=14, weight="bold")
        )
        status_title.pack(anchor="w", padx=10, pady=5)
        
        self.status_label = ctk.CTkLabel(
            status_frame,
            text="准备就绪，点击开始登录",
            font=ctk.CTkFont(size=12),
            text_color="gray"
        )
        self.status_label.pack(anchor="w", padx=10, pady=5)
        
        # 进度条（可选）
        self.progress_bar = ctk.CTkProgressBar(status_frame)
        self.progress_bar.pack(fill="x", padx=10, pady=5)
        self.progress_bar.set(0)
    
    def create_buttons(self, parent):
        """创建按钮区域"""
        button_frame = ctk.CTkFrame(parent)
        button_frame.pack(fill="x", pady=10)
        
        # 开始登录按钮
        self.start_button = ctk.CTkButton(
            button_frame,
            text="开始登录",
            command=self.start_login,
            width=120,
            height=35
        )
        self.start_button.pack(side="left", padx=10, pady=10)
        
        # 取消按钮
        self.cancel_button = ctk.CTkButton(
            button_frame,
            text="取消",
            command=self.cancel_login,
            width=120,
            height=35
        )
        self.cancel_button.pack(side="right", padx=10, pady=10)
        
        # 检查浏览器按钮
        self.check_button = ctk.CTkButton(
            button_frame,
            text="检查状态",
            command=self.check_browser_status,
            width=120,
            height=35,
            state="disabled"
        )
        self.check_button.pack(side="left", padx=10, pady=10)
    
    def create_log_section(self, parent):
        """创建日志显示区域"""
        log_frame = ctk.CTkFrame(parent)
        log_frame.pack(fill="both", expand=True, pady=10)
        
        log_title = ctk.CTkLabel(
            log_frame,
            text="操作日志:",
            font=ctk.CTkFont(size=14, weight="bold")
        )
        log_title.pack(anchor="w", padx=10, pady=5)
        
        # 日志文本框
        self.log_text = scrolledtext.ScrolledText(
            log_frame,
            height=8,
            bg="#2b2b2b",
            fg="white",
            font=("Consolas", 10),
            wrap="word"
        )
        self.log_text.pack(fill="both", expand=True, padx=10, pady=5)
        
        # 添加初始日志
        self.add_log("系统就绪，等待用户操作...")
    
    def start_login(self):
        """开始登录"""
        try:
            self.add_log("正在启动Web浏览器...")
            self.update_status("正在启动浏览器...", "orange")
            self.progress_bar.set(0.2)
            
            # 禁用开始按钮
            self.start_button.configure(state="disabled")
            self.check_button.configure(state="normal")
            
            # 在后台线程中启动浏览器
            def start_browser():
                success = web_auth.start_web_login()
                if not success:
                    self.window.after(0, lambda: self.on_login_status_change("error", "启动浏览器失败"))
            
            threading.Thread(target=start_browser, daemon=True).start()
            
        except Exception as e:
            error_msg = f"启动登录失败: {str(e)}"
            self.add_log(error_msg)
            self.update_status("启动失败", "red")
            messagebox.showerror("错误", error_msg)
    
    def cancel_login(self):
        """取消登录"""
        try:
            self.add_log("正在取消登录...")
            web_auth.close_browser()
            self.update_status("已取消", "gray")
            self.progress_bar.set(0)
            self.start_button.configure(state="normal")
            self.check_button.configure(state="disabled")
            
        except Exception as e:
            self.add_log(f"取消登录失败: {str(e)}")
    
    def check_browser_status(self):
        """检查浏览器状态"""
        try:
            if web_auth.is_browser_open():
                self.add_log("浏览器正在运行中，检查登录状态...")

                # 手动检查登录状态
                result = web_auth.manual_check_login()
                status = result.get("status", "error")
                message = result.get("message", "")

                if status == "success":
                    self.add_log("手动检测到登录成功！")
                    self.update_status("登录成功！", "green")
                elif status == "waiting":
                    self.add_log(f"等待登录中: {message}")
                    self.update_status("等待登录中，请在浏览器中完成登录", "blue")
                else:
                    self.add_log(f"检查失败: {message}")
                    self.update_status("检查失败", "red")
            else:
                self.add_log("浏览器已关闭")
                self.update_status("浏览器已关闭", "gray")
                self.start_button.configure(state="normal")
                self.check_button.configure(state="disabled")

        except Exception as e:
            self.add_log(f"检查状态失败: {str(e)}")
    
    def on_login_status_change(self, status: str, message: str):
        """登录状态变化回调"""
        self.window.after(0, lambda: self._handle_status_change(status, message))
    
    def _handle_status_change(self, status: str, message: str):
        """处理状态变化"""
        self.add_log(f"状态更新: {message}")

        if status == "browser_opened":
            self.update_status("浏览器已打开，请手动完成登录", "blue")
            self.progress_bar.set(0.2)

        elif status == "waiting":
            self.update_status(message, "blue")
            self.progress_bar.set(0.3)

        elif status == "monitoring":
            self.update_status(message, "orange")
            self.progress_bar.set(0.5)

        elif status == "login_detected":
            self.update_status(message, "green")
            self.progress_bar.set(0.8)

        elif status == "success":
            self.update_status("登录成功！", "green")
            self.progress_bar.set(1.0)
            self.start_button.configure(state="normal")
            self.check_button.configure(state="disabled")

            # 显示成功消息
            messagebox.showinfo("登录成功", "Cookie已成功保存，可以开始使用自动回帖功能了！")

            # 调用成功回调
            if self.success_callback:
                self.success_callback({"nickname": "Web登录用户"})

            # 延迟关闭窗口
            self.window.after(3000, self.window.destroy)

        elif status == "cancelled":
            self.update_status("用户取消了登录", "orange")
            self.progress_bar.set(0)
            self.start_button.configure(state="normal")
            self.check_button.configure(state="disabled")

        elif status == "timeout":
            self.update_status("登录等待超时", "red")
            self.progress_bar.set(0)
            self.start_button.configure(state="normal")
            self.check_button.configure(state="disabled")
            messagebox.showwarning("超时", "登录等待超时，请重新尝试登录")

        elif status == "error":
            self.update_status(f"登录失败: {message}", "red")
            self.progress_bar.set(0)
            self.start_button.configure(state="normal")
            self.check_button.configure(state="disabled")
            messagebox.showerror("登录失败", message)
    
    def update_status(self, message: str, color: str = "gray"):
        """更新状态显示"""
        self.status_label.configure(text=message, text_color=color)
    
    def add_log(self, message: str):
        """添加日志"""
        import datetime
        timestamp = datetime.datetime.now().strftime("%H:%M:%S")
        log_entry = f"[{timestamp}] {message}\n"
        
        self.log_text.insert(tk.END, log_entry)
        self.log_text.see(tk.END)
        
        # 记录到系统日志
        logger.info(f"Web登录: {message}")
    
    def on_closing(self):
        """窗口关闭事件"""
        try:
            # 关闭浏览器
            web_auth.close_browser()
            self.window.destroy()
        except Exception as e:
            logger.error(f"关闭Web登录窗口失败: {str(e)}")
            self.window.destroy()
