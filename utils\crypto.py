"""
加密解密工具模块
"""
import hashlib
import hmac
import base64
import time
import random
import string
import json
import os
from urllib.parse import urlencode
from typing import Dict, Any

class XiaohongshuCrypto:
    """小红书加密工具类"""
    
    def __init__(self):
        self.app_id = "xhs-pc-web"
        self.sign_version = "1"
        self.sign_type = "x2"
        self.sign_svn = "56"
    
    def generate_x_s(self, payload: str) -> str:
        """生成X-s签名"""
        # 这是一个简化的签名生成，实际的小红书签名算法更复杂
        # 在实际使用中，可能需要逆向工程或使用浏览器自动化来获取真实签名
        
        sign_data = {
            "signSvn": self.sign_svn,
            "signType": self.sign_type,
            "appId": self.app_id,
            "signVersion": self.sign_version,
            "payload": payload
        }
        
        # 将字典转换为JSON字符串，然后进行base64编码
        json_str = json.dumps(sign_data, separators=(',', ':'))
        encoded = base64.b64encode(json_str.encode()).decode()
        
        return f"XYW_{encoded}"
    
    def generate_x_s_common(self, url: str, data: Dict[str, Any] = None) -> str:
        """生成X-S-Common签名"""
        # 这也是一个简化的实现
        # 实际的X-S-Common签名包含更多的参数和复杂的算法
        
        timestamp = str(int(time.time() * 1000))
        nonce = ''.join(random.choices(string.ascii_letters + string.digits, k=16))
        
        # 构建签名字符串
        sign_str = f"{url}{timestamp}{nonce}"
        if data:
            sign_str += json.dumps(data, separators=(',', ':'))
        
        # 生成签名
        signature = hashlib.md5(sign_str.encode()).hexdigest()
        
        # 构建最终的X-S-Common值
        common_data = {
            "timestamp": timestamp,
            "nonce": nonce,
            "signature": signature
        }
        
        return base64.b64encode(json.dumps(common_data).encode()).decode()
    
    def generate_x_t(self) -> str:
        """生成X-t时间戳"""
        return str(int(time.time() * 1000))
    
    def generate_trace_id(self) -> str:
        """生成追踪ID"""
        return ''.join(random.choices(string.ascii_lowercase + string.digits, k=32))
    
    def generate_device_fingerprint(self) -> Dict[str, str]:
        """生成设备指纹"""
        return {
            "webId": ''.join(random.choices(string.ascii_lowercase + string.digits, k=32)),
            "abRequestId": f"{random.randint(10000000, 99999999)}-{random.randint(1000, 9999)}-{random.randint(1000, 9999)}-{random.randint(1000, 9999)}-{random.randint(100000000000, 999999999999)}",
            "customerClientId": str(random.randint(100000000000000, 999999999999999))
        }
    
    def sign_request(self, method: str, url: str, data: Dict[str, Any] = None, 
                    cookies: Dict[str, str] = None) -> Dict[str, str]:
        """为请求生成完整的签名头"""
        
        # 生成基础头部
        headers = {
            "X-t": self.generate_x_t(),
            "x-b3-traceid": self.generate_trace_id(),
            "x-xray-traceid": self.generate_trace_id(),
        }
        
        # 生成payload用于X-s签名
        if data:
            payload = hashlib.md5(json.dumps(data, separators=(',', ':')).encode()).hexdigest()
        else:
            payload = hashlib.md5(b'').hexdigest()
        
        headers["X-s"] = self.generate_x_s(payload)
        headers["X-S-Common"] = self.generate_x_s_common(url, data)
        
        return headers

class CookieManager:
    """Cookie管理器"""
    
    def __init__(self):
        self.cookie_file = "data/cookies.json"
    
    def save_cookies(self, cookies: Dict[str, str], user_id: str = None):
        """保存cookies"""
        cookie_data = {
            "user_id": user_id,
            "cookies": cookies,
            "timestamp": int(time.time())
        }
        
        try:
            with open(self.cookie_file, 'w', encoding='utf-8') as f:
                json.dump(cookie_data, f, ensure_ascii=False, indent=2)
        except Exception as e:
            print(f"保存cookies失败: {e}")
    
    def load_cookies(self) -> Dict[str, str]:
        """加载cookies"""
        try:
            with open(self.cookie_file, 'r', encoding='utf-8') as f:
                cookie_data = json.load(f)
                return cookie_data.get("cookies", {})
        except Exception:
            return {}
    
    def is_cookies_valid(self) -> bool:
        """检查cookies是否有效"""
        try:
            with open(self.cookie_file, 'r', encoding='utf-8') as f:
                cookie_data = json.load(f)
                timestamp = cookie_data.get("timestamp", 0)
                # 检查cookies是否在24小时内
                return (int(time.time()) - timestamp) < 86400
        except Exception:
            return False
    
    def clear_cookies(self):
        """清除cookies"""
        try:
            if os.path.exists(self.cookie_file):
                os.remove(self.cookie_file)
        except Exception as e:
            print(f"清除cookies失败: {e}")

# 全局实例
crypto = XiaohongshuCrypto()
cookie_manager = CookieManager()
