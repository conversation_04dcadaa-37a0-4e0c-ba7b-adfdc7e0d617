@echo off
chcp 65001 >nul
title 安装小红书自动回帖机器人依赖

echo ========================================
echo 小红书自动回帖机器人 - 依赖安装
echo ========================================
echo.

echo 正在检查Python环境...
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ 未找到Python环境，请先安装Python 3.8+
    echo 下载地址: https://www.python.org/downloads/
    pause
    exit /b 1
)

python --version
echo ✓ Python环境检查通过
echo.

echo 正在升级pip...
python -m pip install --upgrade pip
echo.

echo 正在安装依赖包...
echo 这可能需要几分钟时间，请耐心等待...
echo.

pip install -r requirements.txt

if errorlevel 1 (
    echo.
    echo ❌ 依赖安装失败，请检查网络连接或尝试以下命令:
    echo pip install -i https://pypi.tuna.tsinghua.edu.cn/simple -r requirements.txt
    pause
    exit /b 1
)

echo.
echo ✓ 依赖安装完成！
echo.
echo 现在可以运行程序了:
echo   方式1: 双击 run.bat
echo   方式2: 运行 python main.py
echo.

pause
