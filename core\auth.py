"""
用户认证模块
"""
import requests
import qrcode
import io
import base64
import time
import json
from typing import Dict, Optional, Callable
from PIL import Image

from config.settings import settings
from config.database import db
from utils.logger import logger
from utils.crypto import crypto, cookie_manager
from utils.helpers import retry_on_failure

class XiaohongshuAuth:
    """小红书认证类"""
    
    def __init__(self):
        self.session = requests.Session()
        self.base_url = settings.get("api.base_url")
        self.headers = settings.get_headers()
        self.qr_code_data = None
        self.login_callback = None
    
    def set_login_callback(self, callback: Callable[[str, str], None]):
        """设置登录状态回调函数"""
        self.login_callback = callback
    
    def generate_qr_code(self) -> Optional[str]:
        """生成登录二维码"""
        try:
            # 由于这是演示版本，我们生成一个示例二维码
            # 实际使用时需要替换为真实的小红书登录API

            # 生成示例二维码内容
            qr_content = "https://www.xiaohongshu.com/login?demo=true&timestamp=" + str(int(time.time()))

            # 保存二维码数据用于状态检查
            self.qr_code_data = {
                "qr_id": "demo_qr_" + str(int(time.time())),
                "code": "demo_code_" + str(int(time.time())),
                "qr_content": qr_content
            }

            # 生成二维码图片
            qr = qrcode.QRCode(version=1, box_size=10, border=5)
            qr.add_data(qr_content)
            qr.make(fit=True)

            img = qr.make_image(fill_color="black", back_color="white")

            # 转换为base64字符串
            buffer = io.BytesIO()
            img.save(buffer, format='PNG')
            img_str = base64.b64encode(buffer.getvalue()).decode()

            logger.log_login_attempt("演示二维码生成")
            return img_str

        except Exception as e:
            logger.log_login_error(f"二维码生成异常: {str(e)}")
            return None
    
    @retry_on_failure(max_retries=3)
    def check_qr_status(self) -> Dict[str, str]:
        """检查二维码扫描状态"""
        if not self.qr_code_data:
            return {"status": "error", "message": "二维码数据不存在"}

        try:
            # 演示模式：模拟扫码过程
            # 实际使用时需要替换为真实的小红书API

            # 模拟扫码状态变化
            import random
            current_time = time.time()
            qr_create_time = int(self.qr_code_data.get("qr_id", "demo_qr_0").split("_")[-1])
            elapsed_time = current_time - qr_create_time

            if elapsed_time > 120:  # 2分钟后过期
                return {"status": "expired", "message": "二维码已过期"}
            elif elapsed_time > 30:  # 30秒后模拟扫描成功
                # 随机决定是否"登录成功"
                if random.random() < 0.1:  # 10%概率模拟登录成功
                    # 模拟用户信息
                    user_info = {
                        "user_id": "demo_user_" + str(int(current_time)),
                        "nickname": "演示用户",
                        "avatar": "https://example.com/avatar.jpg"
                    }

                    # 模拟cookies
                    cookies = {
                        "web_session": "demo_session_" + str(int(current_time)),
                        "xsec_token": "demo_token_" + str(int(current_time))
                    }

                    # 保存登录信息
                    self.save_login_session(user_info, cookies)

                    logger.log_login_success(user_info.get("user_id", "unknown"))

                    if self.login_callback:
                        self.login_callback("success", "登录成功")

                    return {
                        "status": "success",
                        "message": "登录成功",
                        "user_info": user_info
                    }
                else:
                    return {"status": "scanned", "message": "已扫描，等待确认"}
            elif elapsed_time > 10:  # 10秒后模拟已扫描
                return {"status": "scanned", "message": "已扫描，等待确认"}
            else:
                return {"status": "waiting", "message": "等待扫描"}

        except Exception as e:
            logger.log_login_error(f"检查二维码状态异常: {str(e)}")
            return {"status": "error", "message": f"检查状态异常: {str(e)}"}
    
    def save_login_session(self, user_info: Dict, cookies: Dict):
        """保存登录会话"""
        try:
            user_id = user_info.get("user_id", "")
            
            # 保存到数据库
            db.save_session(
                user_id=user_id,
                cookies=cookies,
                headers=self.headers
            )
            
            # 保存到文件
            cookie_manager.save_cookies(cookies, user_id)
            
            logger.info(f"登录会话已保存: 用户ID {user_id}")
            
        except Exception as e:
            logger.error(f"保存登录会话失败: {str(e)}")
    
    def load_saved_session(self) -> bool:
        """加载已保存的会话"""
        try:
            # 从数据库加载
            session_data = db.get_active_session()
            if session_data:
                cookies = session_data.get("cookies", {})
                headers = session_data.get("headers", {})
                
                # 更新session
                self.session.cookies.update(cookies)
                self.headers.update(headers)
                
                # 验证会话是否有效
                if self.verify_session():
                    logger.info("已加载有效的登录会话")
                    return True
            
            # 从文件加载
            if cookie_manager.is_cookies_valid():
                cookies = cookie_manager.load_cookies()
                self.session.cookies.update(cookies)
                
                if self.verify_session():
                    logger.info("已从文件加载有效的登录会话")
                    return True
            
            return False
            
        except Exception as e:
            logger.error(f"加载登录会话失败: {str(e)}")
            return False
    
    def verify_session(self) -> bool:
        """验证当前会话是否有效"""
        try:
            # 请求用户信息接口验证
            verify_url = f"{self.base_url}/api/sns/web/v1/user/info"
            
            sign_headers = crypto.sign_request("GET", verify_url)
            headers = {**self.headers, **sign_headers}
            
            response = self.session.get(verify_url, headers=headers, timeout=10)
            
            if response.status_code == 200:
                data = response.json()
                return data.get("success", False)
            
            return False
            
        except Exception as e:
            logger.error(f"验证会话失败: {str(e)}")
            return False
    
    def logout(self):
        """登出"""
        try:
            # 清除会话
            self.session.cookies.clear()
            
            # 清除保存的cookies
            cookie_manager.clear_cookies()
            
            # 更新数据库中的会话状态
            # 这里可以添加更新数据库的逻辑
            
            logger.info("已登出")
            
            if self.login_callback:
                self.login_callback("logout", "已登出")
                
        except Exception as e:
            logger.error(f"登出失败: {str(e)}")
    
    def get_user_info(self) -> Optional[Dict]:
        """获取当前用户信息"""
        try:
            user_url = f"{self.base_url}/api/sns/web/v1/user/info"
            
            sign_headers = crypto.sign_request("GET", user_url)
            headers = {**self.headers, **sign_headers}
            
            response = self.session.get(user_url, headers=headers, timeout=10)
            
            if response.status_code == 200:
                data = response.json()
                if data.get("success"):
                    return data.get("data", {})
            
            return None
            
        except Exception as e:
            logger.error(f"获取用户信息失败: {str(e)}")
            return None
    
    def is_logged_in(self) -> bool:
        """检查是否已登录"""
        return self.verify_session()

# 全局认证实例
auth = XiaohongshuAuth()
