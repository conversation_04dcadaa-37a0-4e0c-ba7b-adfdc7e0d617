"""
登录检测机制测试脚本
"""
import sys
import os
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def test_login_detection_logic():
    """测试登录检测逻辑"""
    print("测试登录检测逻辑...")
    
    try:
        from core.web_auth import WebAuth
        
        # 创建测试实例
        auth = WebAuth()
        
        # 模拟初始cookies（访问网站时的基础cookies）
        initial_cookies = {
            'xhsTrackerId': 'test_tracker_id',
            '_ga': 'test_ga_value',
            '_gid': 'test_gid_value'
        }
        
        # 模拟未登录状态的cookies（只是增加了一些基础cookies）
        not_logged_in_cookies = {
            **initial_cookies,
            'sessionId': 'temp_session',
            'visitId': 'temp_visit'
        }
        
        # 模拟已登录状态的cookies（包含关键登录cookies）
        logged_in_cookies = {
            **initial_cookies,
            'web_session': 'real_session_token',
            'a1': 'real_a1_token',
            'webId': 'real_web_id',
            'userId': 'real_user_id',
            'xsec_token': 'real_xsec_token',
            'sessionId': 'real_session',
            'visitId': 'real_visit',
            'userProfile': 'real_profile'
        }
        
        # 测试URL
        login_url = "https://www.xiaohongshu.com/"
        explore_url = "https://www.xiaohongshu.com/explore"
        
        print("\n1. 测试未登录状态检测:")
        result1 = auth._check_login_status(login_url, not_logged_in_cookies, initial_cookies)
        print(f"   结果: {result1} (应该为 False)")
        
        print("\n2. 测试已登录状态检测:")
        result2 = auth._check_login_status(explore_url, logged_in_cookies, initial_cookies)
        print(f"   结果: {result2} (应该为 True)")
        
        print("\n3. 测试边界情况 - Cookie增加但无关键Cookie:")
        boundary_cookies = {
            **initial_cookies,
            'temp1': 'value1',
            'temp2': 'value2',
            'temp3': 'value3',
            'temp4': 'value4',
            'temp5': 'value5'
        }
        result3 = auth._check_login_status(login_url, boundary_cookies, initial_cookies)
        print(f"   结果: {result3} (应该为 False)")
        
        # 统计测试结果
        expected_results = [False, True, False]
        actual_results = [result1, result2, result3]
        
        passed = sum(1 for expected, actual in zip(expected_results, actual_results) if expected == actual)
        total = len(expected_results)
        
        print(f"\n检测逻辑测试: {passed}/{total} 项通过")
        
        return passed == total
        
    except Exception as e:
        print(f"测试失败: {str(e)}")
        return False

def test_timing_mechanism():
    """测试时间机制"""
    print("\n测试时间机制...")
    
    try:
        print("✓ 最小等待时间: 2分钟 (120秒)")
        print("✓ 检测间隔: 5秒")
        print("✓ 状态更新间隔: 30秒")
        print("✓ 最大等待时间: 30分钟 (1800秒)")
        
        # 计算检测次数
        min_wait = 120  # 2分钟
        max_wait = 1800  # 30分钟
        check_interval = 5  # 5秒
        
        detection_period = max_wait - min_wait  # 实际检测时间
        detection_count = detection_period // check_interval  # 检测次数
        
        print(f"\n时间分析:")
        print(f"• 等待期: {min_wait}秒 ({min_wait//60}分钟)")
        print(f"• 检测期: {detection_period}秒 ({detection_period//60}分钟)")
        print(f"• 总检测次数: {detection_count}次")
        print(f"• 平均每分钟检测: {60//check_interval}次")
        
        return True
        
    except Exception as e:
        print(f"时间机制测试失败: {str(e)}")
        return False

def show_detection_criteria():
    """显示检测标准"""
    print("\n" + "="*60)
    print("登录检测标准说明")
    print("="*60)
    
    print("""
🔍 多重检测机制 (需满足至少3/5个条件):

1. Cookie数量显著增加 (≥5个新Cookie)
   • 登录前: 基础Cookie (3-5个)
   • 登录后: 完整Cookie (10+个)

2. 关键登录Cookie存在 (≥2个)
   • web_session: 会话令牌
   • a1: 认证令牌  
   • webId: 设备标识

3. URL变化检测
   • /explore: 发现页面
   • /user/: 用户页面
   • /feed: 动态页面
   • /home: 首页

4. 页面标题检测
   • 包含: 小红书、发现、首页、RedBook、Home、Explore
   • 排除: 登录、login

5. 页面元素检测
   • 用户头像元素
   • 用户名元素
   • 个人资料元素

⏰ 时间控制:
• 前2分钟: 纯等待，不检测 (避免误判)
• 2-30分钟: 每5秒检测一次
• 30分钟后: 超时退出

💡 设计理念:
• 宁可漏检，不可误检
• 给用户充足的操作时间
• 多重验证确保准确性
""")

def main():
    """主测试函数"""
    print("🧪 登录检测机制测试")
    print("="*50)
    
    tests = [
        ("登录检测逻辑", test_login_detection_logic),
        ("时间机制", test_timing_mechanism)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        try:
            print(f"\n🔍 测试: {test_name}")
            if test_func():
                passed += 1
                print(f"✅ {test_name} 测试通过")
            else:
                print(f"❌ {test_name} 测试失败")
        except Exception as e:
            print(f"❌ {test_name} 测试异常: {e}")
    
    print("\n" + "="*50)
    print(f"测试完成: {passed}/{total} 项测试通过")
    
    if passed == total:
        print("🎉 所有测试通过！检测机制已优化。")
    else:
        print("❌ 部分测试失败，请检查相关逻辑。")
    
    # 显示检测标准
    show_detection_criteria()
    
    print("\n💡 现在可以测试实际登录:")
    print("python main.py")
    print("选择 'Web浏览器登录' 体验优化后的检测机制")
    
    return passed == total

if __name__ == "__main__":
    success = main()
    input("\n按回车键退出...")
    sys.exit(0 if success else 1)
