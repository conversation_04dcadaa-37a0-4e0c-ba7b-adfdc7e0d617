"""
自动回复模块
"""
import requests
import json
import time
import random
from typing import List, Dict, Any, Optional

from config.settings import settings
from config.database import db
from utils.logger import logger
from utils.crypto import crypto
from utils.helpers import retry_on_failure, rate_limit_delay, generate_reply_content
from core.auth import auth

class XiaohongshuReplyEngine:
    """小红书自动回复引擎"""
    
    def __init__(self, session: requests.Session = None):
        self.session = session or requests.Session()
        self.base_url = settings.get("api.base_url")
        self.comment_endpoint = settings.get("api.comment_endpoint", "/api/sns/web/v1/comment/post")
        self.headers = settings.get_headers()
        self.reply_config = settings.get_reply_config()
        
        # 回复统计
        self.daily_reply_count = 0
        self.last_reset_date = time.strftime("%Y-%m-%d")
        
    def update_session(self, session: requests.Session):
        """更新会话"""
        self.session = session
    
    def can_reply(self, note_id: str) -> bool:
        """检查是否可以回复"""
        # 检查每日回复限制
        current_date = time.strftime("%Y-%m-%d")
        if current_date != self.last_reset_date:
            self.daily_reply_count = 0
            self.last_reset_date = current_date
        
        max_daily_replies = self.reply_config.get("max_daily_replies", 100)
        if self.daily_reply_count >= max_daily_replies:
            logger.warning(f"已达到每日回复限制: {max_daily_replies}")
            return False
        
        # 检查是否已经回复过这个笔记
        if self._has_replied_to_note(note_id):
            logger.info(f"笔记 {note_id} 已经回复过，跳过")
            return False
        
        return True
    
    def _has_replied_to_note(self, note_id: str) -> bool:
        """检查是否已经回复过指定笔记"""
        try:
            # 这里应该查询数据库检查回复记录
            # 简化实现，实际应该查询reply_records表
            return False
        except Exception as e:
            logger.error(f"检查回复记录失败: {str(e)}")
            return False
    
    @retry_on_failure(max_retries=3, delay=2.0)
    def reply_to_note(self, note_id: str, note_title: str = None,
                     note_content: str = None, custom_reply: str = None) -> Dict[str, Any]:
        """回复笔记"""
        try:
            # 检查登录状态
            if not auth.is_logged_in():
                return {
                    "success": False,
                    "error": "用户未登录",
                    "note_id": note_id
                }

            # 检查是否可以回复
            if not self.can_reply(note_id):
                return {
                    "success": False,
                    "error": "无法回复此笔记",
                    "note_id": note_id
                }

            # 生成回复内容
            if custom_reply:
                reply_content = custom_reply
            else:
                templates = self.reply_config.get("templates", [])
                reply_content = generate_reply_content(templates, note_title, note_content)

            # 验证回复内容
            if not reply_content or len(reply_content.strip()) == 0:
                return {
                    "success": False,
                    "error": "回复内容不能为空",
                    "note_id": note_id
                }

            # 记录回复尝试
            logger.log_reply_attempt(note_id, reply_content)

            # 添加回复记录到数据库
            record_id = self._add_reply_record(note_id, reply_content)

            # 发送回复请求
            result = self._send_reply_request(note_id, reply_content)

            if result.get("success"):
                # 更新回复状态为成功
                if record_id:
                    db.update_reply_status(record_id, "success")

                self.daily_reply_count += 1
                logger.log_reply_success(note_id)

                # 添加随机延迟
                reply_interval = self.reply_config.get("reply_interval", 60)
                rate_limit_delay(reply_interval, reply_interval + 30)

                return {
                    "success": True,
                    "note_id": note_id,
                    "reply_content": reply_content,
                    "message": "回复成功"
                }
            else:
                # 更新回复状态为失败
                error_msg = result.get("error", "回复失败")
                if record_id:
                    db.update_reply_status(record_id, "failed", error_msg)

                logger.log_reply_error(note_id, error_msg)

                return {
                    "success": False,
                    "note_id": note_id,
                    "error": error_msg
                }

        except Exception as e:
            error_msg = f"回复异常: {str(e)}"
            logger.log_reply_error(note_id, error_msg)
            return {
                "success": False,
                "note_id": note_id,
                "error": error_msg
            }
    
    def _send_reply_request(self, note_id: str, reply_content: str) -> Dict[str, Any]:
        """发送回复请求"""
        try:
            url = f"{self.base_url}{self.comment_endpoint}"
            
            # 构建回复数据
            comment_data = {
                "note_id": note_id,
                "content": reply_content,
                "at_users": [],
                "reply_to_comment_id": "",
                "reply_to_user_id": "",
                "image_list": []
            }
            
            # 生成签名头
            sign_headers = crypto.sign_request("POST", url, comment_data)
            headers = {
                **self.headers,
                **sign_headers,
                "Content-Type": "application/json;charset=UTF-8"
            }
            
            # 使用认证模块的session发送请求
            start_time = time.time()
            response = auth.session.post(
                url,
                json=comment_data,
                headers=headers,
                timeout=15
            )
            response_time = time.time() - start_time
            
            # 记录API请求日志
            logger.log_api_request("POST", url, response.status_code, response_time)
            
            if response.status_code == 200:
                data = response.json()
                if data.get("success") or data.get("code") == 0:
                    return {"success": True, "data": data}
                else:
                    error_msg = data.get("msg", "回复失败")
                    return {"success": False, "error": error_msg}
            else:
                return {"success": False, "error": f"HTTP错误: {response.status_code}"}
                
        except Exception as e:
            return {"success": False, "error": f"请求异常: {str(e)}"}
    
    def _add_reply_record(self, note_id: str, reply_content: str) -> Optional[int]:
        """添加回复记录"""
        try:
            db.add_reply_record(note_id, reply_content, "pending")
            # 这里应该返回记录ID，简化实现
            return 1
        except Exception as e:
            logger.error(f"添加回复记录失败: {str(e)}")
            return None
    
    def batch_reply(self, notes: List[Dict[str, Any]], 
                   custom_replies: Dict[str, str] = None) -> List[Dict[str, Any]]:
        """批量回复笔记"""
        results = []
        custom_replies = custom_replies or {}
        
        for note in notes:
            note_id = note.get("id", "")
            if not note_id:
                continue
            
            # 获取自定义回复内容
            custom_reply = custom_replies.get(note_id)
            
            # 执行回复
            result = self.reply_to_note(
                note_id=note_id,
                note_title=note.get("title", ""),
                note_content=note.get("content", ""),
                custom_reply=custom_reply
            )
            
            results.append(result)
            
            # 如果回复失败且是严重错误，停止批量回复
            if not result.get("success"):
                error = result.get("error", "")
                if "登录" in error or "权限" in error or "封禁" in error:
                    logger.error(f"严重错误，停止批量回复: {error}")
                    break
        
        return results
    
    def get_reply_templates(self) -> List[str]:
        """获取回复模板"""
        return self.reply_config.get("templates", [])
    
    def add_reply_template(self, template: str) -> bool:
        """添加回复模板"""
        try:
            templates = self.reply_config.get("templates", [])
            if template not in templates:
                templates.append(template)
                settings.set("reply.templates", templates)
                return True
            return False
        except Exception as e:
            logger.error(f"添加回复模板失败: {str(e)}")
            return False
    
    def remove_reply_template(self, template: str) -> bool:
        """删除回复模板"""
        try:
            templates = self.reply_config.get("templates", [])
            if template in templates:
                templates.remove(template)
                settings.set("reply.templates", templates)
                return True
            return False
        except Exception as e:
            logger.error(f"删除回复模板失败: {str(e)}")
            return False
    
    def get_daily_stats(self) -> Dict[str, int]:
        """获取每日统计"""
        return {
            "daily_reply_count": self.daily_reply_count,
            "max_daily_replies": self.reply_config.get("max_daily_replies", 100),
            "remaining_replies": max(0, self.reply_config.get("max_daily_replies", 100) - self.daily_reply_count)
        }
    
    def reset_daily_count(self):
        """重置每日计数"""
        self.daily_reply_count = 0
        self.last_reset_date = time.strftime("%Y-%m-%d")
        logger.info("每日回复计数已重置")

# 全局回复引擎实例
reply_engine = XiaohongshuReplyEngine()
