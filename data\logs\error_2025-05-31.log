2025-05-31 11:22:42 - <PERSON><PERSON><PERSON><PERSON><PERSON> - ERROR - 登录失败: 二维码生成失败: 404
2025-05-31 12:33:52 - <PERSON><PERSON><PERSON><PERSON>ot - ERROR - Cookie监控异常: Message: invalid session id: session deleted as the browser has closed the connection
from disconnected: not connected to DevTools
  (Session info: chrome=136.0.7103.114)
Stacktrace:
	GetHandleVerifier [0x0118FC03+61635]
	GetHandleVerifier [0x0118FC44+61700]
	(No symbol) [0x00FB05D3]
	(No symbol) [0x00F9FE20]
	(No symbol) [0x00FBDD1F]
	(No symbol) [0x01023E8C]
	(No symbol) [0x0103DF19]
	(No symbol) [0x0101D096]
	(No symbol) [0x00FEC840]
	(No symbol) [0x00FED6A4]
	GetHandleVerifier [0x01414523+2701795]
	GetHandleVerifier [0x0140FCA6+2683238]
	GetHandleVerifier [0x0142A9EE+2793134]
	GetHandleVerifier [0x011A68C5+155013]
	GetHandleVerifier [0x011ACFAD+181357]
	GetHandleVerifier [0x01197458+92440]
	GetHandleVerifier [0x01197600+92864]
	GetHandleVerifier [0x01181FF0+5296]
	BaseThreadInitThunk [0x75C65D49+25]
	RtlInitializeExceptionChain [0x77A9D09B+107]
	RtlGetAppContainerNamedObjectPath [0x77A9D021+561]

2025-05-31 12:33:57 - XiaohongshuBot - ERROR - Cookie监控异常: Message: invalid session id
Stacktrace:
	GetHandleVerifier [0x0118FC03+61635]
	GetHandleVerifier [0x0118FC44+61700]
	(No symbol) [0x00FB044E]
	(No symbol) [0x00FEBB08]
	(No symbol) [0x0101D156]
	(No symbol) [0x01018C22]
	(No symbol) [0x010181B6]
	(No symbol) [0x00F836C5]
	(No symbol) [0x00F83C1E]
	(No symbol) [0x00F840AD]
	GetHandleVerifier [0x01414523+2701795]
	GetHandleVerifier [0x0140FCA6+2683238]
	GetHandleVerifier [0x0142A9EE+2793134]
	GetHandleVerifier [0x011A68C5+155013]
	GetHandleVerifier [0x011ACFAD+181357]
	(No symbol) [0x00F83390]
	(No symbol) [0x00F82B9D]
	GetHandleVerifier [0x0150877C+3701820]
	BaseThreadInitThunk [0x75C65D49+25]
	RtlInitializeExceptionChain [0x77A9D09B+107]
	RtlGetAppContainerNamedObjectPath [0x77A9D021+561]

2025-05-31 14:45:42 - XiaohongshuBot - ERROR - 刷新任务列表失败: Display column #0 cannot be set
2025-05-31 14:45:42 - XiaohongshuBot - CRITICAL - 程序运行异常: 'MainWindow' object has no attribute 'status_label'
2025-05-31 14:45:42 - XiaohongshuBot - CRITICAL - Traceback (most recent call last):
  File "D:\AI\pc_tool\xiaohongshu_auto_reply_robot\gui\main_window.py", line 531, in refresh_task_list
    self.task_tree.set(item_id, "#0", str(task_id))
  File "D:\ProgramData\anaconda3\Lib\tkinter\ttk.py", line 1437, in set
    res = self.tk.call(self._w, "set", item, column, value)
          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
_tkinter.TclError: Display column #0 cannot be set

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "D:\AI\pc_tool\xiaohongshu_auto_reply_robot\main.py", line 102, in main
    app = MainWindow()
          ^^^^^^^^^^^^
  File "D:\AI\pc_tool\xiaohongshu_auto_reply_robot\gui\main_window.py", line 29, in __init__
    self.setup_ui()
  File "D:\AI\pc_tool\xiaohongshu_auto_reply_robot\gui\main_window.py", line 71, in setup_ui
    self.create_right_panel()
  File "D:\AI\pc_tool\xiaohongshu_auto_reply_robot\gui\main_window.py", line 226, in create_right_panel
    self.create_task_tab()
  File "D:\AI\pc_tool\xiaohongshu_auto_reply_robot\gui\main_window.py", line 265, in create_task_tab
    self.refresh_task_list()
  File "D:\AI\pc_tool\xiaohongshu_auto_reply_robot\gui\main_window.py", line 535, in refresh_task_list
    self.update_status(f"刷新任务列表失败: {str(e)}")
  File "D:\AI\pc_tool\xiaohongshu_auto_reply_robot\gui\main_window.py", line 488, in update_status
    self.status_label.configure(text=message)
    ^^^^^^^^^^^^^^^^^
AttributeError: 'MainWindow' object has no attribute 'status_label'

2025-05-31 14:45:52 - XiaohongshuBot - ERROR - 刷新任务列表失败: Display column #0 cannot be set
2025-05-31 14:45:52 - XiaohongshuBot - CRITICAL - 程序运行异常: 'MainWindow' object has no attribute 'status_label'
2025-05-31 14:45:52 - XiaohongshuBot - CRITICAL - Traceback (most recent call last):
  File "D:\AI\pc_tool\xiaohongshu_auto_reply_robot\gui\main_window.py", line 531, in refresh_task_list
    self.task_tree.set(item_id, "#0", str(task_id))
  File "D:\ProgramData\anaconda3\Lib\tkinter\ttk.py", line 1437, in set
    res = self.tk.call(self._w, "set", item, column, value)
          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
_tkinter.TclError: Display column #0 cannot be set

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "D:\AI\pc_tool\xiaohongshu_auto_reply_robot\main.py", line 102, in main
    app = MainWindow()
          ^^^^^^^^^^^^
  File "D:\AI\pc_tool\xiaohongshu_auto_reply_robot\gui\main_window.py", line 29, in __init__
    self.setup_ui()
  File "D:\AI\pc_tool\xiaohongshu_auto_reply_robot\gui\main_window.py", line 71, in setup_ui
    self.create_right_panel()
  File "D:\AI\pc_tool\xiaohongshu_auto_reply_robot\gui\main_window.py", line 226, in create_right_panel
    self.create_task_tab()
  File "D:\AI\pc_tool\xiaohongshu_auto_reply_robot\gui\main_window.py", line 265, in create_task_tab
    self.refresh_task_list()
  File "D:\AI\pc_tool\xiaohongshu_auto_reply_robot\gui\main_window.py", line 535, in refresh_task_list
    self.update_status(f"刷新任务列表失败: {str(e)}")
  File "D:\AI\pc_tool\xiaohongshu_auto_reply_robot\gui\main_window.py", line 488, in update_status
    self.status_label.configure(text=message)
    ^^^^^^^^^^^^^^^^^
AttributeError: 'MainWindow' object has no attribute 'status_label'

2025-05-31 16:42:37 - XiaohongshuBot - ERROR - 刷新任务列表失败: 'MainWindow' object has no attribute 'task_tree'
