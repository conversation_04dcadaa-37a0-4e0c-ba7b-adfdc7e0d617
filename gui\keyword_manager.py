"""
关键词管理窗口
"""
import tkinter as tk
from tkinter import ttk, messagebox, simpledialog
import customtkinter as ctk

from config.database import db
from utils.logger import logger
from utils.helpers import is_valid_keyword

class KeywordManagerWindow:
    """关键词管理窗口类"""
    
    def __init__(self, parent):
        self.parent = parent
        
        # 创建窗口
        self.window = ctk.CTkToplevel(parent)
        self.setup_window()
        self.setup_ui()
        
        # 加载关键词列表
        self.refresh_keyword_list()
    
    def setup_window(self):
        """设置窗口属性"""
        self.window.title("关键词管理")
        self.window.geometry("600x500")
        
        # 设置为模态窗口
        self.window.transient(self.parent)
        self.window.grab_set()
        
        # 居中显示
        self.center_window()
    
    def center_window(self):
        """窗口居中"""
        self.window.update_idletasks()
        x = (self.window.winfo_screenwidth() // 2) - (600 // 2)
        y = (self.window.winfo_screenheight() // 2) - (500 // 2)
        self.window.geometry(f"600x500+{x}+{y}")
    
    def setup_ui(self):
        """设置用户界面"""
        # 主框架
        main_frame = ctk.CTkFrame(self.window)
        main_frame.pack(fill="both", expand=True, padx=20, pady=20)
        
        # 标题
        title_label = ctk.CTkLabel(
            main_frame,
            text="关键词管理",
            font=ctk.CTkFont(size=20, weight="bold")
        )
        title_label.pack(pady=10)
        
        # 添加关键词部分
        self.create_add_section(main_frame)
        
        # 关键词列表部分
        self.create_list_section(main_frame)
        
        # 操作按钮部分
        self.create_button_section(main_frame)
    
    def create_add_section(self, parent):
        """创建添加关键词部分"""
        add_frame = ctk.CTkFrame(parent)
        add_frame.pack(fill="x", pady=10)
        
        # 标签
        add_label = ctk.CTkLabel(
            add_frame,
            text="添加新关键词:",
            font=ctk.CTkFont(size=14)
        )
        add_label.pack(side="left", padx=10, pady=10)
        
        # 输入框
        self.keyword_entry = ctk.CTkEntry(
            add_frame,
            placeholder_text="输入关键词",
            width=200
        )
        self.keyword_entry.pack(side="left", padx=10, pady=10)
        
        # 绑定回车键
        self.keyword_entry.bind("<Return>", lambda e: self.add_keyword())
        
        # 添加按钮
        add_button = ctk.CTkButton(
            add_frame,
            text="添加",
            command=self.add_keyword,
            width=80
        )
        add_button.pack(side="left", padx=10, pady=10)
        
        # 批量导入按钮
        import_button = ctk.CTkButton(
            add_frame,
            text="批量导入",
            command=self.batch_import,
            width=100
        )
        import_button.pack(side="right", padx=10, pady=10)
    
    def create_list_section(self, parent):
        """创建关键词列表部分"""
        list_frame = ctk.CTkFrame(parent)
        list_frame.pack(fill="both", expand=True, pady=10)
        
        # 列表标题
        list_label = ctk.CTkLabel(
            list_frame,
            text="关键词列表:",
            font=ctk.CTkFont(size=14)
        )
        list_label.pack(anchor="w", padx=10, pady=5)
        
        # 创建Treeview
        self.tree_frame = tk.Frame(list_frame, bg="#2b2b2b")
        self.tree_frame.pack(fill="both", expand=True, padx=10, pady=5)
        
        # 关键词列表
        self.keyword_tree = ttk.Treeview(
            self.tree_frame,
            columns=("keyword", "enabled", "search_count", "reply_count", "created_at"),
            show="headings",
            height=12
        )
        
        # 设置列标题
        self.keyword_tree.heading("keyword", text="关键词")
        self.keyword_tree.heading("enabled", text="状态")
        self.keyword_tree.heading("search_count", text="搜索次数")
        self.keyword_tree.heading("reply_count", text="回复次数")
        self.keyword_tree.heading("created_at", text="创建时间")
        
        # 设置列宽
        self.keyword_tree.column("keyword", width=150)
        self.keyword_tree.column("enabled", width=80)
        self.keyword_tree.column("search_count", width=100)
        self.keyword_tree.column("reply_count", width=100)
        self.keyword_tree.column("created_at", width=150)
        
        # 滚动条
        scrollbar = ttk.Scrollbar(self.tree_frame, orient="vertical", command=self.keyword_tree.yview)
        self.keyword_tree.configure(yscrollcommand=scrollbar.set)
        
        # 布局
        self.keyword_tree.pack(side="left", fill="both", expand=True)
        scrollbar.pack(side="right", fill="y")
        
        # 绑定双击事件
        self.keyword_tree.bind("<Double-1>", self.on_item_double_click)
    
    def create_button_section(self, parent):
        """创建操作按钮部分"""
        button_frame = ctk.CTkFrame(parent)
        button_frame.pack(fill="x", pady=10)
        
        # 左侧按钮
        left_buttons = [
            ("启用", self.enable_keyword),
            ("禁用", self.disable_keyword),
            ("编辑", self.edit_keyword),
            ("删除", self.delete_keyword)
        ]
        
        for text, command in left_buttons:
            button = ctk.CTkButton(
                button_frame,
                text=text,
                command=command,
                width=80,
                height=30
            )
            button.pack(side="left", padx=5, pady=10)
        
        # 右侧按钮
        right_buttons = [
            ("刷新", self.refresh_keyword_list),
            ("导出", self.export_keywords),
            ("清空", self.clear_all_keywords)
        ]
        
        for text, command in right_buttons:
            button = ctk.CTkButton(
                button_frame,
                text=text,
                command=command,
                width=80,
                height=30
            )
            button.pack(side="right", padx=5, pady=10)
    
    def add_keyword(self):
        """添加关键词"""
        keyword = self.keyword_entry.get().strip()
        
        if not keyword:
            messagebox.showwarning("警告", "请输入关键词")
            return
        
        if not is_valid_keyword(keyword):
            messagebox.showwarning("警告", "关键词格式不正确")
            return
        
        try:
            if db.add_keyword(keyword):
                self.keyword_entry.delete(0, "end")
                self.refresh_keyword_list()
                logger.info(f"已添加关键词: {keyword}")
                messagebox.showinfo("成功", f"关键词 '{keyword}' 添加成功")
            else:
                messagebox.showwarning("警告", "关键词已存在")
        
        except Exception as e:
            logger.error(f"添加关键词失败: {str(e)}")
            messagebox.showerror("错误", f"添加关键词失败: {str(e)}")
    
    def batch_import(self):
        """批量导入关键词"""
        # 创建批量导入对话框
        import_window = ctk.CTkToplevel(self.window)
        import_window.title("批量导入关键词")
        import_window.geometry("400x300")
        import_window.transient(self.window)
        import_window.grab_set()
        
        # 说明标签
        info_label = ctk.CTkLabel(
            import_window,
            text="请输入关键词，每行一个:",
            font=ctk.CTkFont(size=14)
        )
        info_label.pack(pady=10)
        
        # 文本框
        text_widget = tk.Text(
            import_window,
            height=10,
            width=40,
            bg="#2b2b2b",
            fg="white",
            font=("Arial", 12)
        )
        text_widget.pack(pady=10, padx=20, fill="both", expand=True)
        
        # 按钮框架
        button_frame = ctk.CTkFrame(import_window)
        button_frame.pack(pady=10)
        
        def do_import():
            content = text_widget.get("1.0", "end-1c")
            keywords = [line.strip() for line in content.split("\n") if line.strip()]
            
            if not keywords:
                messagebox.showwarning("警告", "请输入关键词")
                return
            
            success_count = 0
            for keyword in keywords:
                if is_valid_keyword(keyword):
                    try:
                        if db.add_keyword(keyword):
                            success_count += 1
                    except:
                        pass
            
            self.refresh_keyword_list()
            messagebox.showinfo("完成", f"成功导入 {success_count} 个关键词")
            import_window.destroy()
        
        # 导入按钮
        import_btn = ctk.CTkButton(
            button_frame,
            text="导入",
            command=do_import,
            width=80
        )
        import_btn.pack(side="left", padx=10)
        
        # 取消按钮
        cancel_btn = ctk.CTkButton(
            button_frame,
            text="取消",
            command=import_window.destroy,
            width=80
        )
        cancel_btn.pack(side="right", padx=10)
    
    def refresh_keyword_list(self):
        """刷新关键词列表"""
        try:
            # 清空现有项目
            for item in self.keyword_tree.get_children():
                self.keyword_tree.delete(item)
            
            # 获取关键词列表
            keywords = db.get_keywords(enabled_only=False)
            
            # 添加到列表
            for keyword in keywords:
                status = "启用" if keyword.get("enabled") else "禁用"
                created_at = keyword.get("created_at", "")[:19]  # 只显示日期时间部分
                
                self.keyword_tree.insert("", "end", values=(
                    keyword.get("keyword", ""),
                    status,
                    keyword.get("search_count", 0),
                    keyword.get("reply_count", 0),
                    created_at
                ))
        
        except Exception as e:
            logger.error(f"刷新关键词列表失败: {str(e)}")
            messagebox.showerror("错误", f"刷新列表失败: {str(e)}")
    
    def get_selected_keyword_id(self):
        """获取选中的关键词ID"""
        selection = self.keyword_tree.selection()
        if not selection:
            messagebox.showwarning("警告", "请选择一个关键词")
            return None
        
        # 这里需要根据实际情况获取关键词ID
        # 简化实现，实际应该在插入时保存ID
        item = self.keyword_tree.item(selection[0])
        keyword = item["values"][0]
        
        # 通过关键词查找ID
        keywords = db.get_keywords(enabled_only=False)
        for kw in keywords:
            if kw.get("keyword") == keyword:
                return kw.get("id")
        
        return None
    
    def enable_keyword(self):
        """启用关键词"""
        keyword_id = self.get_selected_keyword_id()
        if keyword_id:
            try:
                if db.update_keyword(keyword_id, enabled=True):
                    self.refresh_keyword_list()
                    messagebox.showinfo("成功", "关键词已启用")
                else:
                    messagebox.showerror("错误", "启用关键词失败")
            except Exception as e:
                messagebox.showerror("错误", f"启用关键词失败: {str(e)}")
    
    def disable_keyword(self):
        """禁用关键词"""
        keyword_id = self.get_selected_keyword_id()
        if keyword_id:
            try:
                if db.update_keyword(keyword_id, enabled=False):
                    self.refresh_keyword_list()
                    messagebox.showinfo("成功", "关键词已禁用")
                else:
                    messagebox.showerror("错误", "禁用关键词失败")
            except Exception as e:
                messagebox.showerror("错误", f"禁用关键词失败: {str(e)}")
    
    def edit_keyword(self):
        """编辑关键词"""
        selection = self.keyword_tree.selection()
        if not selection:
            messagebox.showwarning("警告", "请选择一个关键词")
            return
        
        item = self.keyword_tree.item(selection[0])
        current_keyword = item["values"][0]
        
        # 创建编辑对话框
        new_keyword = simpledialog.askstring(
            "编辑关键词",
            "请输入新的关键词:",
            initialvalue=current_keyword
        )
        
        if new_keyword and new_keyword != current_keyword:
            if is_valid_keyword(new_keyword):
                keyword_id = self.get_selected_keyword_id()
                if keyword_id:
                    try:
                        if db.update_keyword(keyword_id, keyword=new_keyword):
                            self.refresh_keyword_list()
                            messagebox.showinfo("成功", "关键词已更新")
                        else:
                            messagebox.showerror("错误", "更新关键词失败")
                    except Exception as e:
                        messagebox.showerror("错误", f"更新关键词失败: {str(e)}")
            else:
                messagebox.showwarning("警告", "关键词格式不正确")
    
    def delete_keyword(self):
        """删除关键词"""
        selection = self.keyword_tree.selection()
        if not selection:
            messagebox.showwarning("警告", "请选择一个关键词")
            return
        
        item = self.keyword_tree.item(selection[0])
        keyword = item["values"][0]
        
        if messagebox.askyesno("确认删除", f"确定要删除关键词 '{keyword}' 吗？"):
            keyword_id = self.get_selected_keyword_id()
            if keyword_id:
                try:
                    if db.delete_keyword(keyword_id):
                        self.refresh_keyword_list()
                        messagebox.showinfo("成功", "关键词已删除")
                    else:
                        messagebox.showerror("错误", "删除关键词失败")
                except Exception as e:
                    messagebox.showerror("错误", f"删除关键词失败: {str(e)}")
    
    def export_keywords(self):
        """导出关键词"""
        try:
            keywords = db.get_keywords(enabled_only=False)
            keyword_list = [kw.get("keyword", "") for kw in keywords]
            
            if keyword_list:
                # 这里可以实现导出到文件的功能
                content = "\n".join(keyword_list)
                messagebox.showinfo("导出", f"共 {len(keyword_list)} 个关键词:\n\n{content[:200]}...")
            else:
                messagebox.showinfo("提示", "没有关键词可导出")
        
        except Exception as e:
            messagebox.showerror("错误", f"导出关键词失败: {str(e)}")
    
    def clear_all_keywords(self):
        """清空所有关键词"""
        if messagebox.askyesno("确认清空", "确定要删除所有关键词吗？此操作不可恢复！"):
            try:
                keywords = db.get_keywords(enabled_only=False)
                for keyword in keywords:
                    db.delete_keyword(keyword.get("id"))
                
                self.refresh_keyword_list()
                messagebox.showinfo("成功", "所有关键词已清空")
            
            except Exception as e:
                messagebox.showerror("错误", f"清空关键词失败: {str(e)}")
    
    def on_item_double_click(self, event):
        """双击事件处理"""
        self.edit_keyword()
