"""
配置管理模块
"""
import json
import os
from typing import Dict, Any

class Settings:
    def __init__(self):
        self.config_file = "data/config.json"
        self.default_config = {
            "app": {
                "name": "小红书自动回帖机器人",
                "version": "1.0.0",
                "author": "AI Assistant"
            },
            "api": {
                "base_url": "https://edith.xiaohongshu.com",
                "search_endpoint": "/api/sns/web/v1/search/notes",
                "recommend_endpoint": "/api/sns/web/v1/search/recommend",
                "comment_endpoint": "/api/sns/web/v1/comment/post"
            },
            "headers": {
                "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
                "Accept": "application/json, text/plain, */*",
                "Accept-Language": "zh-C<PERSON>,zh;q=0.9,en-US;q=0.8,en;q=0.7,zh-TW;q=0.6",
                "Accept-Encoding": "gzip, deflate, br, zstd",
                "Origin": "https://www.xiaohongshu.com",
                "Referer": "https://www.xiaohongshu.com/",
                "Sec-Fetch-Site": "same-site",
                "Sec-Fetch-Mode": "cors",
                "Sec-Fetch-Dest": "empty",
                "sec-ch-ua": '"Chromium";v="136", "Google Chrome";v="136", "Not.A/Brand";v="99"',
                "sec-ch-ua-mobile": "?0",
                "sec-ch-ua-platform": '"Windows"'
            },
            "task": {
                "max_concurrent_tasks": 5,
                "search_interval": 30,  # 搜索间隔(秒)
                "reply_interval": 60,   # 回复间隔(秒)
                "max_replies_per_note": 1,
                "max_daily_replies": 100
            },
            "reply": {
                "templates": [
                    "很棒的分享！👍",
                    "学到了，感谢分享！",
                    "这个很实用，收藏了！",
                    "太有用了，谢谢楼主！",
                    "赞同，很有道理！",
                    "好详细的教程，点赞！"
                ],
                "enable_ai_reply": False,
                "ai_model": "gpt-3.5-turbo"
            },
            "gui": {
                "theme": "dark",
                "window_size": "1200x800",
                "font_family": "Microsoft YaHei",
                "font_size": 12
            }
        }
        self.config = self.load_config()
    
    def load_config(self) -> Dict[str, Any]:
        """加载配置文件"""
        if not os.path.exists("data"):
            os.makedirs("data")
        
        if os.path.exists(self.config_file):
            try:
                with open(self.config_file, 'r', encoding='utf-8') as f:
                    config = json.load(f)
                # 合并默认配置和用户配置
                return self._merge_config(self.default_config, config)
            except Exception as e:
                print(f"加载配置文件失败: {e}")
                return self.default_config.copy()
        else:
            # 创建默认配置文件
            self.save_config(self.default_config)
            return self.default_config.copy()
    
    def save_config(self, config: Dict[str, Any] = None):
        """保存配置文件"""
        if config is None:
            config = self.config
        
        try:
            with open(self.config_file, 'w', encoding='utf-8') as f:
                json.dump(config, f, ensure_ascii=False, indent=2)
        except Exception as e:
            print(f"保存配置文件失败: {e}")
    
    def _merge_config(self, default: Dict, user: Dict) -> Dict:
        """合并配置"""
        result = default.copy()
        for key, value in user.items():
            if key in result and isinstance(result[key], dict) and isinstance(value, dict):
                result[key] = self._merge_config(result[key], value)
            else:
                result[key] = value
        return result
    
    def get(self, key: str, default=None):
        """获取配置值"""
        keys = key.split('.')
        value = self.config
        for k in keys:
            if isinstance(value, dict) and k in value:
                value = value[k]
            else:
                return default
        return value
    
    def set(self, key: str, value: Any):
        """设置配置值"""
        keys = key.split('.')
        config = self.config
        for k in keys[:-1]:
            if k not in config:
                config[k] = {}
            config = config[k]
        config[keys[-1]] = value
        self.save_config()
    
    def get_headers(self) -> Dict[str, str]:
        """获取请求头"""
        return self.get("headers", {})
    
    def get_api_config(self) -> Dict[str, str]:
        """获取API配置"""
        return self.get("api", {})
    
    def get_task_config(self) -> Dict[str, Any]:
        """获取任务配置"""
        return self.get("task", {})
    
    def get_reply_config(self) -> Dict[str, Any]:
        """获取回复配置"""
        return self.get("reply", {})

# 全局配置实例
settings = Settings()
