"""
直接登录功能测试脚本
"""
import sys
import os
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def test_direct_login():
    """测试直接登录功能"""
    print("测试直接登录功能...")
    
    try:
        # 测试主窗口创建
        import tkinter as tk
        import customtkinter as ctk
        
        # 创建测试根窗口
        root = tk.Tk()
        root.withdraw()  # 隐藏主窗口
        
        # 测试Web登录窗口导入
        from gui.web_login_window import WebLoginWindow
        print("✓ Web登录窗口模块导入成功")
        
        # 测试Web认证模块
        from core.web_auth import web_auth
        print("✓ Web认证模块导入成功")
        
        # 测试主窗口模块
        from gui.main_window import MainWindow
        print("✓ 主窗口模块导入成功")
        
        root.destroy()
        return True
        
    except Exception as e:
        print(f"✗ 直接登录功能测试失败: {e}")
        return False

def test_simplified_workflow():
    """测试简化的工作流程"""
    print("\n测试简化的工作流程...")
    
    try:
        from core.web_auth import WebAuth
        
        # 创建认证实例
        auth = WebAuth()
        
        # 测试回调设置
        def test_callback(status, message):
            print(f"  回调: {status} - {message}")
        
        auth.set_login_callback(test_callback)
        print("✓ 登录回调设置成功")
        
        # 测试获取说明
        instructions = auth.get_login_instructions()
        if instructions and "Chrome" in instructions:
            print("✓ 登录说明获取成功")
        else:
            print("✗ 登录说明获取失败")
            return False
        
        # 测试状态检查
        is_open = auth.is_browser_open()
        print(f"✓ 浏览器状态检查: {is_open}")
        
        return True
        
    except Exception as e:
        print(f"✗ 工作流程测试失败: {e}")
        return False

def show_usage_guide():
    """显示使用指南"""
    print("\n" + "="*60)
    print("直接登录功能使用指南")
    print("="*60)
    
    print("""
🚀 简化的登录流程:

1. 启动程序
   python main.py

2. 点击"登录"按钮
   • 直接弹出Web浏览器登录窗口
   • 无需选择登录方式

3. 浏览器自动打开
   • 自动导航到小红书官网
   • 开始2分钟等待期

4. 完成登录操作
   • 手机号码 + 验证码
   • 邮箱 + 密码
   • 扫描二维码
   • 第三方账号登录

5. 自动检测和保存
   • 2分钟后开始检测
   • 多重验证机制
   • 自动保存Cookie

⏰ 时间安排:
• 0-2分钟: 纯等待，不检测
• 2-30分钟: 自动检测登录状态
• 检测间隔: 每5秒一次

🔍 检测标准:
• Cookie数量显著增加 (≥5个)
• 关键登录Cookie存在 (≥2个)
• URL变化检测
• 页面标题检测
• 页面元素检测
• 需满足至少3/5个条件

💡 优势:
• 一键登录，无需选择
• 支持所有官方登录方式
• 严格的检测机制
• 充足的操作时间
• 自动保存Cookie

⚠️ 注意:
• 需要Chrome浏览器
• 首次使用会下载ChromeDriver
• 登录过程中不要关闭浏览器
• 如需取消，直接关闭浏览器即可
""")

def main():
    """主测试函数"""
    print("🧪 直接登录功能测试")
    print("="*50)
    
    tests = [
        ("直接登录功能", test_direct_login),
        ("简化工作流程", test_simplified_workflow)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        try:
            print(f"\n🔍 测试: {test_name}")
            if test_func():
                passed += 1
                print(f"✅ {test_name} 测试通过")
            else:
                print(f"❌ {test_name} 测试失败")
        except Exception as e:
            print(f"❌ {test_name} 测试异常: {e}")
    
    print("\n" + "="*50)
    print(f"测试完成: {passed}/{total} 项测试通过")
    
    if passed == total:
        print("🎉 所有测试通过！直接登录功能已就绪。")
        show_usage_guide()
        return True
    else:
        print("❌ 部分测试失败，请检查相关模块。")
        return False

if __name__ == "__main__":
    success = main()
    input("\n按回车键退出...")
    sys.exit(0 if success else 1)
