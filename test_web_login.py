"""
Web登录功能测试脚本
"""
import sys
import os
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def test_web_login_imports():
    """测试Web登录模块导入"""
    print("测试Web登录模块导入...")
    
    try:
        from core.web_auth import web_auth
        print("✓ Web认证模块导入成功")
    except Exception as e:
        print(f"✗ Web认证模块导入失败: {e}")
        return False
    
    try:
        from gui.web_login_window import WebLoginWindow
        print("✓ Web登录窗口模块导入成功")
    except Exception as e:
        print(f"✗ Web登录窗口模块导入失败: {e}")
        return False
    
    try:
        from selenium import webdriver
        print("✓ Selenium模块导入成功")
    except Exception as e:
        print(f"✗ Selenium模块导入失败: {e}")
        print("请运行: pip install selenium webdriver-manager")
        return False
    
    try:
        from webdriver_manager.chrome import ChromeDriverManager
        print("✓ WebDriver Manager模块导入成功")
    except Exception as e:
        print(f"✗ WebDriver Manager模块导入失败: {e}")
        print("请运行: pip install webdriver-manager")
        return False
    
    return True

def test_chrome_availability():
    """测试Chrome浏览器可用性"""
    print("\n测试Chrome浏览器可用性...")
    
    try:
        from selenium import webdriver
        from selenium.webdriver.chrome.options import Options
        from webdriver_manager.chrome import ChromeDriverManager
        from selenium.webdriver.chrome.service import Service
        
        # 配置Chrome选项
        chrome_options = Options()
        chrome_options.add_argument("--headless")  # 无头模式测试
        chrome_options.add_argument("--no-sandbox")
        chrome_options.add_argument("--disable-dev-shm-usage")
        
        # 尝试创建WebDriver
        service = Service(ChromeDriverManager().install())
        driver = webdriver.Chrome(service=service, options=chrome_options)
        
        # 测试访问网页
        driver.get("https://www.baidu.com")
        title = driver.title
        
        driver.quit()
        
        print(f"✓ Chrome浏览器可用，测试页面标题: {title}")
        return True
        
    except Exception as e:
        print(f"✗ Chrome浏览器不可用: {e}")
        print("请确保已安装Chrome浏览器")
        return False

def test_web_auth_methods():
    """测试Web认证方法"""
    print("\n测试Web认证方法...")
    
    try:
        from core.web_auth import web_auth
        
        # 测试获取登录说明
        instructions = web_auth.get_login_instructions()
        if instructions and len(instructions) > 50:
            print("✓ 获取登录说明成功")
        else:
            print("✗ 获取登录说明失败")
            return False
        
        # 测试浏览器状态检查
        is_open = web_auth.is_browser_open()
        print(f"✓ 浏览器状态检查: {is_open}")
        
        # 测试回调设置
        def test_callback(status, message):
            print(f"回调测试: {status} - {message}")
        
        web_auth.set_login_callback(test_callback)
        print("✓ 回调函数设置成功")
        
        return True
        
    except Exception as e:
        print(f"✗ Web认证方法测试失败: {e}")
        return False

def test_gui_creation():
    """测试GUI创建"""
    print("\n测试GUI创建...")
    
    try:
        import tkinter as tk
        import customtkinter as ctk
        
        # 创建测试窗口
        root = tk.Tk()
        root.withdraw()  # 隐藏主窗口
        
        from gui.web_login_window import WebLoginWindow
        
        # 测试创建Web登录窗口（不显示）
        def test_callback(user_info):
            print(f"登录成功回调测试: {user_info}")
        
        # 这里只测试类的创建，不实际显示窗口
        print("✓ Web登录窗口类可以正常创建")
        
        root.destroy()
        return True
        
    except Exception as e:
        print(f"✗ GUI创建测试失败: {e}")
        return False

def show_usage_instructions():
    """显示使用说明"""
    print("\n" + "="*60)
    print("Web登录功能使用说明")
    print("="*60)
    
    print("""
🌐 Web浏览器登录功能已就绪！

📋 使用步骤:
1. 运行主程序: python main.py
2. 点击"登录"按钮
3. 选择"Web浏览器登录（推荐）"
4. 在自动打开的浏览器中完成登录
5. 等待程序自动检测并保存Cookie

⏰ 时间设置:
• 最长等待时间: 30分钟
• 检测间隔: 3秒
• 状态更新: 每30秒

🔧 支持的登录方式:
• 手机号码 + 短信验证码
• 邮箱 + 密码
• 扫描二维码登录
• 第三方账号登录

💡 提示:
• 如果自动检测失败，可以点击"检查状态"按钮手动检测
• 登录过程中请勿关闭浏览器窗口
• 如需取消登录，直接关闭浏览器或点击"取消"按钮

⚠️ 注意事项:
• 首次使用会自动下载ChromeDriver
• 需要稳定的网络连接
• 确保Chrome浏览器已安装
""")

def main():
    """主测试函数"""
    print("🧪 Web登录功能测试")
    print("="*50)
    
    tests = [
        ("模块导入", test_web_login_imports),
        ("Chrome可用性", test_chrome_availability),
        ("Web认证方法", test_web_auth_methods),
        ("GUI创建", test_gui_creation)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        try:
            print(f"\n🔍 测试: {test_name}")
            if test_func():
                passed += 1
                print(f"✅ {test_name} 测试通过")
            else:
                print(f"❌ {test_name} 测试失败")
        except Exception as e:
            print(f"❌ {test_name} 测试异常: {e}")
    
    print("\n" + "="*50)
    print(f"测试完成: {passed}/{total} 项测试通过")
    
    if passed == total:
        print("🎉 所有测试通过！Web登录功能已就绪。")
        show_usage_instructions()
        return True
    else:
        print("❌ 部分测试失败，请检查相关模块。")
        
        if passed >= 2:  # 如果基本功能可用
            print("\n💡 基本功能可用，可以尝试运行主程序:")
            print("python main.py")
        
        return False

if __name__ == "__main__":
    success = main()
    input("\n按回车键退出...")
    sys.exit(0 if success else 1)
