"""
任务监控窗口
"""
import tkinter as tk
from tkinter import ttk, messagebox
import customtkinter as ctk
import threading
import time
from datetime import datetime

from core.task_manager import task_manager
from utils.logger import logger

class TaskMonitorWindow:
    """任务监控窗口类"""
    
    def __init__(self, parent):
        self.parent = parent
        
        # 创建窗口
        self.window = ctk.CTkToplevel(parent)
        self.setup_window()
        self.setup_ui()
        
        # 启动监控
        self.is_monitoring = True
        self.start_monitoring()
    
    def setup_window(self):
        """设置窗口属性"""
        self.window.title("任务监控")
        self.window.geometry("800x600")
        
        # 设置为模态窗口
        self.window.transient(self.parent)
        self.window.grab_set()
        
        # 居中显示
        self.center_window()
        
        # 设置关闭事件
        self.window.protocol("WM_DELETE_WINDOW", self.on_closing)
    
    def center_window(self):
        """窗口居中"""
        self.window.update_idletasks()
        x = (self.window.winfo_screenwidth() // 2) - (800 // 2)
        y = (self.window.winfo_screenheight() // 2) - (600 // 2)
        self.window.geometry(f"800x600+{x}+{y}")
    
    def setup_ui(self):
        """设置用户界面"""
        # 主框架
        main_frame = ctk.CTkFrame(self.window)
        main_frame.pack(fill="both", expand=True, padx=20, pady=20)
        
        # 标题
        title_label = ctk.CTkLabel(
            main_frame,
            text="任务监控",
            font=ctk.CTkFont(size=20, weight="bold")
        )
        title_label.pack(pady=10)
        
        # 创建选项卡
        self.notebook = ttk.Notebook(main_frame)
        self.notebook.pack(fill="both", expand=True, pady=10)
        
        # 任务状态选项卡
        self.create_status_tab()
        
        # 实时日志选项卡
        self.create_log_tab()
        
        # 性能监控选项卡
        self.create_performance_tab()
    
    def create_status_tab(self):
        """创建任务状态选项卡"""
        self.status_frame = ctk.CTkFrame(self.notebook)
        self.notebook.add(self.status_frame, text="任务状态")
        
        # 任务列表
        self.create_task_list()
        
        # 任务详情
        self.create_task_details()
        
        # 控制按钮
        self.create_control_buttons()
    
    def create_task_list(self):
        """创建任务列表"""
        list_frame = ctk.CTkFrame(self.status_frame)
        list_frame.pack(fill="both", expand=True, padx=10, pady=10)
        
        # 标题
        list_label = ctk.CTkLabel(
            list_frame,
            text="任务列表:",
            font=ctk.CTkFont(size=14, weight="bold")
        )
        list_label.pack(anchor="w", padx=10, pady=5)
        
        # 任务树形视图
        tree_frame = tk.Frame(list_frame, bg="#2b2b2b")
        tree_frame.pack(fill="both", expand=True, padx=10, pady=5)
        
        self.task_tree = ttk.Treeview(
            tree_frame,
            columns=("name", "status", "keywords", "progress", "last_run"),
            show="headings",
            height=8
        )
        
        # 设置列标题
        self.task_tree.heading("name", text="任务名称")
        self.task_tree.heading("status", text="状态")
        self.task_tree.heading("keywords", text="关键词")
        self.task_tree.heading("progress", text="进度")
        self.task_tree.heading("last_run", text="最后运行")
        
        # 设置列宽
        self.task_tree.column("name", width=150)
        self.task_tree.column("status", width=80)
        self.task_tree.column("keywords", width=200)
        self.task_tree.column("progress", width=120)
        self.task_tree.column("last_run", width=150)
        
        # 滚动条
        scrollbar = ttk.Scrollbar(tree_frame, orient="vertical", command=self.task_tree.yview)
        self.task_tree.configure(yscrollcommand=scrollbar.set)
        
        # 布局
        self.task_tree.pack(side="left", fill="both", expand=True)
        scrollbar.pack(side="right", fill="y")
        
        # 绑定选择事件
        self.task_tree.bind("<<TreeviewSelect>>", self.on_task_select)
    
    def create_task_details(self):
        """创建任务详情"""
        details_frame = ctk.CTkFrame(self.status_frame)
        details_frame.pack(fill="x", padx=10, pady=5)
        
        # 标题
        details_label = ctk.CTkLabel(
            details_frame,
            text="任务详情:",
            font=ctk.CTkFont(size=14, weight="bold")
        )
        details_label.pack(anchor="w", padx=10, pady=5)
        
        # 详情文本
        self.details_text = tk.Text(
            details_frame,
            height=6,
            bg="#2b2b2b",
            fg="white",
            font=("Consolas", 10),
            wrap="word",
            state="disabled"
        )
        self.details_text.pack(fill="x", padx=10, pady=5)
    
    def create_control_buttons(self):
        """创建控制按钮"""
        button_frame = ctk.CTkFrame(self.status_frame)
        button_frame.pack(fill="x", padx=10, pady=5)
        
        buttons = [
            ("启动", self.start_task),
            ("停止", self.stop_task),
            ("暂停", self.pause_task),
            ("恢复", self.resume_task),
            ("刷新", self.refresh_tasks)
        ]
        
        for text, command in buttons:
            button = ctk.CTkButton(
                button_frame,
                text=text,
                command=command,
                width=80,
                height=30
            )
            button.pack(side="left", padx=5, pady=10)
    
    def create_log_tab(self):
        """创建实时日志选项卡"""
        self.log_frame = ctk.CTkFrame(self.notebook)
        self.notebook.add(self.log_frame, text="实时日志")
        
        # 日志文本框
        self.log_text = tk.Text(
            self.log_frame,
            bg="#2b2b2b",
            fg="white",
            font=("Consolas", 10),
            wrap="word",
            state="disabled"
        )
        self.log_text.pack(fill="both", expand=True, padx=10, pady=10)
        
        # 滚动条
        log_scrollbar = ttk.Scrollbar(self.log_text)
        log_scrollbar.pack(side="right", fill="y")
        self.log_text.config(yscrollcommand=log_scrollbar.set)
        log_scrollbar.config(command=self.log_text.yview)
        
        # 控制按钮
        log_button_frame = ctk.CTkFrame(self.log_frame)
        log_button_frame.pack(fill="x", padx=10, pady=5)
        
        clear_log_button = ctk.CTkButton(
            log_button_frame,
            text="清空日志",
            command=self.clear_log,
            width=100
        )
        clear_log_button.pack(side="left", padx=5)
        
        save_log_button = ctk.CTkButton(
            log_button_frame,
            text="保存日志",
            command=self.save_log,
            width=100
        )
        save_log_button.pack(side="left", padx=5)
    
    def create_performance_tab(self):
        """创建性能监控选项卡"""
        self.perf_frame = ctk.CTkFrame(self.notebook)
        self.notebook.add(self.perf_frame, text="性能监控")
        
        # 性能指标
        self.create_performance_metrics()
    
    def create_performance_metrics(self):
        """创建性能指标"""
        metrics_frame = ctk.CTkFrame(self.perf_frame)
        metrics_frame.pack(fill="both", expand=True, padx=10, pady=10)
        
        # 标题
        metrics_label = ctk.CTkLabel(
            metrics_frame,
            text="系统性能指标:",
            font=ctk.CTkFont(size=16, weight="bold")
        )
        metrics_label.pack(pady=10)
        
        # 指标网格
        self.metrics_labels = {}
        metrics = [
            ("运行任务数", "running_tasks"),
            ("总搜索次数", "total_searches"),
            ("总回复次数", "total_replies"),
            ("成功回复率", "success_rate"),
            ("平均响应时间", "avg_response_time"),
            ("内存使用", "memory_usage"),
            ("CPU使用率", "cpu_usage"),
            ("网络状态", "network_status")
        ]
        
        # 创建2x4的网格
        for i, (name, key) in enumerate(metrics):
            row = i // 2
            col = i % 2
            
            metric_frame = ctk.CTkFrame(metrics_frame)
            metric_frame.grid(row=row, column=col, padx=10, pady=10, sticky="ew")
            
            name_label = ctk.CTkLabel(
                metric_frame,
                text=name,
                font=ctk.CTkFont(size=12, weight="bold")
            )
            name_label.pack(pady=5)
            
            value_label = ctk.CTkLabel(
                metric_frame,
                text="--",
                font=ctk.CTkFont(size=14)
            )
            value_label.pack(pady=5)
            
            self.metrics_labels[key] = value_label
        
        # 配置网格权重
        metrics_frame.grid_columnconfigure(0, weight=1)
        metrics_frame.grid_columnconfigure(1, weight=1)
    
    def start_monitoring(self):
        """启动监控"""
        def monitor():
            while self.is_monitoring:
                try:
                    # 更新任务列表
                    self.window.after(0, self.refresh_tasks)
                    
                    # 更新性能指标
                    self.window.after(0, self.update_performance_metrics)
                    
                    # 等待5秒
                    time.sleep(5)
                
                except Exception as e:
                    logger.error(f"监控异常: {str(e)}")
                    time.sleep(5)
        
        self.monitor_thread = threading.Thread(target=monitor, daemon=True)
        self.monitor_thread.start()
    
    def refresh_tasks(self):
        """刷新任务列表"""
        try:
            # 清空现有项目
            for item in self.task_tree.get_children():
                self.task_tree.delete(item)
            
            # 获取任务列表
            tasks = task_manager.get_task_list()
            
            # 添加到列表
            for task in tasks:
                keywords_str = ", ".join(task.get("keywords", []))[:30]
                progress = f"{task.get('success_replies', 0)}/{task.get('total_replies', 0)}"
                last_run = task.get("last_run_time", "")
                if last_run:
                    last_run = last_run.strftime("%H:%M:%S") if hasattr(last_run, 'strftime') else str(last_run)[:8]
                
                self.task_tree.insert("", "end", values=(
                    task.get("name", ""),
                    task.get("status", ""),
                    keywords_str,
                    progress,
                    last_run
                ))
        
        except Exception as e:
            logger.error(f"刷新任务列表失败: {str(e)}")
    
    def update_performance_metrics(self):
        """更新性能指标"""
        try:
            # 获取任务统计
            tasks = task_manager.get_task_list()
            running_tasks = len([t for t in tasks if t.get("status") == "running"])
            total_searches = sum(t.get("total_searches", 0) for t in tasks)
            total_replies = sum(t.get("total_replies", 0) for t in tasks)
            success_replies = sum(t.get("success_replies", 0) for t in tasks)
            
            # 计算成功率
            success_rate = (success_replies / total_replies * 100) if total_replies > 0 else 0
            
            # 更新标签
            self.metrics_labels["running_tasks"].configure(text=str(running_tasks))
            self.metrics_labels["total_searches"].configure(text=str(total_searches))
            self.metrics_labels["total_replies"].configure(text=str(total_replies))
            self.metrics_labels["success_rate"].configure(text=f"{success_rate:.1f}%")
            self.metrics_labels["avg_response_time"].configure(text="--")
            self.metrics_labels["memory_usage"].configure(text="--")
            self.metrics_labels["cpu_usage"].configure(text="--")
            self.metrics_labels["network_status"].configure(text="正常")
        
        except Exception as e:
            logger.error(f"更新性能指标失败: {str(e)}")
    
    def on_task_select(self, event):
        """任务选择事件"""
        selection = self.task_tree.selection()
        if selection:
            item = self.task_tree.item(selection[0])
            task_name = item["values"][0]
            
            # 获取任务详情
            tasks = task_manager.get_task_list()
            for task in tasks:
                if task.get("name") == task_name:
                    self.show_task_details(task)
                    break
    
    def show_task_details(self, task):
        """显示任务详情"""
        details = f"""任务名称: {task.get('name', '')}
状态: {task.get('status', '')}
关键词: {', '.join(task.get('keywords', []))}
开始时间: {task.get('start_time', '')}
最后运行: {task.get('last_run_time', '')}
总搜索次数: {task.get('total_searches', 0)}
总回复次数: {task.get('total_replies', 0)}
成功回复: {task.get('success_replies', 0)}
失败回复: {task.get('failed_replies', 0)}
错误信息: {len(task.get('errors', []))} 条错误"""
        
        self.details_text.config(state="normal")
        self.details_text.delete("1.0", "end")
        self.details_text.insert("1.0", details)
        self.details_text.config(state="disabled")
    
    def get_selected_task_id(self):
        """获取选中的任务ID"""
        selection = self.task_tree.selection()
        if not selection:
            messagebox.showwarning("警告", "请选择一个任务")
            return None
        
        item = self.task_tree.item(selection[0])
        task_name = item["values"][0]
        
        # 通过任务名称查找ID
        tasks = task_manager.get_task_list()
        for task in tasks:
            if task.get("name") == task_name:
                return task.get("id")
        
        return None
    
    def start_task(self):
        """启动任务"""
        task_id = self.get_selected_task_id()
        if task_id:
            if task_manager.start_task(task_id):
                messagebox.showinfo("成功", "任务已启动")
            else:
                messagebox.showerror("错误", "启动任务失败")
    
    def stop_task(self):
        """停止任务"""
        task_id = self.get_selected_task_id()
        if task_id:
            if task_manager.stop_task(task_id):
                messagebox.showinfo("成功", "任务已停止")
            else:
                messagebox.showerror("错误", "停止任务失败")
    
    def pause_task(self):
        """暂停任务"""
        task_id = self.get_selected_task_id()
        if task_id:
            if task_manager.pause_task(task_id):
                messagebox.showinfo("成功", "任务已暂停")
            else:
                messagebox.showerror("错误", "暂停任务失败")
    
    def resume_task(self):
        """恢复任务"""
        task_id = self.get_selected_task_id()
        if task_id:
            if task_manager.resume_task(task_id):
                messagebox.showinfo("成功", "任务已恢复")
            else:
                messagebox.showerror("错误", "恢复任务失败")
    
    def add_log_message(self, message):
        """添加日志消息"""
        timestamp = datetime.now().strftime("%H:%M:%S")
        log_entry = f"[{timestamp}] {message}\n"
        
        self.log_text.config(state="normal")
        self.log_text.insert("end", log_entry)
        self.log_text.see("end")
        self.log_text.config(state="disabled")
    
    def clear_log(self):
        """清空日志"""
        self.log_text.config(state="normal")
        self.log_text.delete("1.0", "end")
        self.log_text.config(state="disabled")
    
    def save_log(self):
        """保存日志"""
        # 这里可以实现保存日志到文件的功能
        messagebox.showinfo("提示", "保存日志功能开发中...")
    
    def on_closing(self):
        """窗口关闭事件"""
        self.is_monitoring = False
        self.window.destroy()
