"""
搜索引擎模块
"""
import requests
import json
import time
import random
from typing import List, Dict, Any, Optional
from urllib.parse import urlencode

from config.settings import settings
from config.database import db
from utils.logger import logger
from utils.crypto import crypto
from utils.helpers import retry_on_failure, rate_limit_delay, clean_text, parse_xiaohongshu_time
from core.auth import auth

class XiaohongshuSearchEngine:
    """小红书搜索引擎"""
    
    def __init__(self, session: requests.Session = None):
        self.session = session or requests.Session()
        self.base_url = settings.get("api.base_url")
        self.search_endpoint = settings.get("api.search_endpoint")
        self.recommend_endpoint = settings.get("api.recommend_endpoint")
        self.headers = settings.get_headers()
        
    def update_session(self, session: requests.Session):
        """更新会话"""
        self.session = session
    
    @retry_on_failure(max_retries=3, delay=2.0)
    def search_notes(self, keyword: str, page: int = 1, page_size: int = 20,
                    sort_type: str = "general") -> Dict[str, Any]:
        """搜索笔记"""
        try:
            # 检查登录状态
            if not auth.is_logged_in():
                logger.warning("用户未登录，无法进行搜索")
                return {"success": False, "error": "用户未登录", "notes": []}

            url = f"{self.base_url}{self.search_endpoint}"

            # 构建搜索参数
            search_data = {
                "keyword": keyword,
                "page": page,
                "page_size": page_size,
                "search_id": self._generate_search_id(),
                "sort": sort_type,
                "note_type": 0,
                "ext_flags": [],
                "filters": [
                    {"tags": [sort_type], "type": "sort_type"},
                    {"tags": ["不限"], "type": "filter_note_type"},
                    {"tags": ["不限"], "type": "filter_note_time"},
                    {"tags": ["不限"], "type": "filter_note_range"},
                    {"tags": ["不限"], "type": "filter_pos_distance"}
                ],
                "geo": "",
                "image_formats": ["jpg", "webp", "avif"]
            }

            # 生成签名头
            sign_headers = crypto.sign_request("POST", url, search_data)
            headers = {
                **self.headers,
                **sign_headers,
                "Content-Type": "application/json;charset=UTF-8"
            }

            # 使用认证模块的session发送请求
            start_time = time.time()
            response = auth.session.post(
                url,
                json=search_data,
                headers=headers,
                timeout=15
            )
            response_time = time.time() - start_time

            # 记录API请求日志
            logger.log_api_request("POST", url, response.status_code, response_time)

            if response.status_code == 200:
                data = response.json()
                if data.get("success") or data.get("code") == 0:
                    result = self._parse_search_result(data, keyword)
                    logger.log_search_result(keyword, len(result.get("notes", [])))

                    # 更新关键词搜索统计
                    try:
                        db.update_keyword_stats(keyword, search_count=1)
                    except Exception as e:
                        logger.warning(f"更新关键词统计失败: {str(e)}")

                    # 保存搜索记录
                    self._save_search_records(keyword, result.get("notes", []))

                    # 添加延迟避免请求过频
                    rate_limit_delay(1.0, 3.0)

                    return result
                else:
                    error_msg = data.get("msg", "搜索失败")
                    logger.error(f"搜索API返回错误: {error_msg}")
                    return {"success": False, "error": error_msg, "notes": []}
            else:
                error_msg = f"HTTP错误: {response.status_code}"
                logger.error(f"搜索请求失败: {error_msg}")
                return {"success": False, "error": error_msg, "notes": []}

        except Exception as e:
            logger.error(f"搜索异常: {str(e)}")
            return {"success": False, "error": str(e), "notes": []}
    
    def _parse_search_result(self, data: Dict[str, Any], keyword: str) -> Dict[str, Any]:
        """解析搜索结果"""
        result = {
            "success": True,
            "keyword": keyword,
            "notes": [],
            "has_more": False,
            "total_count": 0
        }
        
        try:
            search_data = data.get("data", {})
            items = search_data.get("items", [])
            result["has_more"] = search_data.get("has_more", False)
            
            for item in items:
                if item.get("model_type") == "note":
                    note_card = item.get("note_card", {})
                    if note_card:
                        note_info = self._extract_note_info(item, note_card)
                        if note_info:
                            result["notes"].append(note_info)
            
            result["total_count"] = len(result["notes"])
            
        except Exception as e:
            logger.error(f"解析搜索结果失败: {str(e)}")
        
        return result
    
    def _extract_note_info(self, item: Dict, note_card: Dict) -> Optional[Dict[str, Any]]:
        """提取笔记信息"""
        try:
            note_info = {
                "id": item.get("id", ""),
                "title": clean_text(note_card.get("display_title", "")),
                "type": note_card.get("type", "normal"),
                "url": f"https://www.xiaohongshu.com/explore/{item.get('id', '')}",
                "xsec_token": item.get("xsec_token", "")
            }
            
            # 用户信息
            user_info = note_card.get("user", {})
            note_info["author"] = {
                "user_id": user_info.get("user_id", ""),
                "nickname": clean_text(user_info.get("nickname", "")),
                "avatar": user_info.get("avatar", ""),
                "xsec_token": user_info.get("xsec_token", "")
            }
            
            # 互动信息
            interact_info = note_card.get("interact_info", {})
            note_info["stats"] = {
                "liked_count": self._parse_count(interact_info.get("liked_count", "0")),
                "collected_count": self._parse_count(interact_info.get("collected_count", "0")),
                "comment_count": self._parse_count(interact_info.get("comment_count", "0")),
                "shared_count": self._parse_count(interact_info.get("shared_count", "0"))
            }
            
            # 发布时间
            corner_tags = note_card.get("corner_tag_info", [])
            for tag in corner_tags:
                if tag.get("type") == "publish_time":
                    note_info["publish_time"] = tag.get("text", "")
                    note_info["publish_datetime"] = parse_xiaohongshu_time(tag.get("text", ""))
                    break
            
            # 图片信息
            image_list = note_card.get("image_list", [])
            note_info["images"] = []
            for img in image_list[:3]:  # 只取前3张图片
                info_list = img.get("info_list", [])
                for info in info_list:
                    if info.get("image_scene") == "WB_DFT":
                        note_info["images"].append({
                            "url": info.get("url", ""),
                            "width": img.get("width", 0),
                            "height": img.get("height", 0)
                        })
                        break
            
            return note_info
            
        except Exception as e:
            logger.error(f"提取笔记信息失败: {str(e)}")
            return None
    
    def _parse_count(self, count_str: str) -> int:
        """解析数量字符串"""
        if not count_str or count_str == "0":
            return 0
        
        try:
            # 处理"1.2万"、"5千"等格式
            if "万" in count_str:
                num = float(count_str.replace("万", ""))
                return int(num * 10000)
            elif "千" in count_str:
                num = float(count_str.replace("千", ""))
                return int(num * 1000)
            else:
                return int(count_str)
        except:
            return 0
    
    def _generate_search_id(self) -> str:
        """生成搜索ID"""
        chars = "abcdefghijklmnopqrstuvwxyz0123456789"
        return ''.join(random.choices(chars, k=20))
    
    def _save_search_records(self, keyword: str, notes: List[Dict]):
        """保存搜索记录"""
        try:
            for note in notes:
                db.add_search_record(
                    keyword=keyword,
                    note_id=note.get("id", ""),
                    note_title=note.get("title", ""),
                    note_author=note.get("author", {}).get("nickname", ""),
                    note_url=note.get("url", "")
                )
        except Exception as e:
            logger.error(f"保存搜索记录失败: {str(e)}")
    
    @retry_on_failure(max_retries=2)
    def get_search_recommendations(self, keyword: str) -> List[str]:
        """获取搜索推荐"""
        try:
            url = f"{self.base_url}{self.recommend_endpoint}"
            params = {"keyword": keyword}
            
            sign_headers = crypto.sign_request("GET", url)
            headers = {**self.headers, **sign_headers}
            
            response = self.session.get(url, params=params, headers=headers, timeout=10)
            
            if response.status_code == 200:
                data = response.json()
                if data.get("success"):
                    sug_items = data.get("data", {}).get("sug_items", [])
                    recommendations = []
                    
                    for item in sug_items:
                        text = item.get("text", "")
                        if text and text != keyword:
                            recommendations.append(text)
                    
                    return recommendations[:10]  # 最多返回10个推荐
            
            return []
            
        except Exception as e:
            logger.error(f"获取搜索推荐失败: {str(e)}")
            return []

# 全局搜索引擎实例
search_engine = XiaohongshuSearchEngine()
