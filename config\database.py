"""
数据库操作模块
"""
import sqlite3
import os
import json
from datetime import datetime
from typing import List, Dict, Any, Optional

from utils.logger import logger

class Database:
    def __init__(self, db_path: str = "data/xiaohongshu.db"):
        self.db_path = db_path
        self.ensure_data_dir()
        self.init_database()
    
    def ensure_data_dir(self):
        """确保数据目录存在"""
        data_dir = os.path.dirname(self.db_path)
        if not os.path.exists(data_dir):
            os.makedirs(data_dir)
    
    def init_database(self):
        """初始化数据库表"""
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()
            
            # 关键词表
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS keywords (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    keyword TEXT NOT NULL UNIQUE,
                    enabled BOOLEAN DEFAULT 1,
                    search_count INTEGER DEFAULT 0,
                    reply_count INTEGER DEFAULT 0,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            ''')
            
            # 任务表
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS tasks (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    name TEXT NOT NULL,
                    keywords TEXT NOT NULL,  -- JSON格式存储关键词列表
                    status TEXT DEFAULT 'stopped',  -- running, stopped, paused
                    config TEXT,  -- JSON格式存储任务配置
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    last_run_at TIMESTAMP
                )
            ''')
            
            # 搜索记录表
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS search_records (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    keyword TEXT NOT NULL,
                    note_id TEXT NOT NULL,
                    note_title TEXT,
                    note_author TEXT,
                    note_url TEXT,
                    searched_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            ''')
            
            # 回复记录表
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS reply_records (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    note_id TEXT NOT NULL,
                    reply_content TEXT NOT NULL,
                    reply_status TEXT DEFAULT 'pending',  -- pending, success, failed
                    error_message TEXT,
                    replied_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            ''')
            
            # 用户会话表
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS user_sessions (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    user_id TEXT,
                    cookies TEXT,  -- JSON格式存储cookies
                    headers TEXT,  -- JSON格式存储headers
                    is_active BOOLEAN DEFAULT 1,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    expires_at TIMESTAMP
                )
            ''')
            
            conn.commit()
    
    # 关键词管理
    def add_keyword(self, keyword: str) -> bool:
        """添加关键词"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                cursor.execute(
                    "INSERT INTO keywords (keyword) VALUES (?)",
                    (keyword,)
                )
                conn.commit()
                return True
        except sqlite3.IntegrityError:
            return False  # 关键词已存在
    
    def get_keywords(self, enabled_only: bool = True) -> List[Dict[str, Any]]:
        """获取关键词列表"""
        with sqlite3.connect(self.db_path) as conn:
            conn.row_factory = sqlite3.Row
            cursor = conn.cursor()
            
            if enabled_only:
                cursor.execute("SELECT * FROM keywords WHERE enabled = 1 ORDER BY created_at DESC")
            else:
                cursor.execute("SELECT * FROM keywords ORDER BY created_at DESC")
            
            return [dict(row) for row in cursor.fetchall()]
    
    def update_keyword(self, keyword_id: int, **kwargs) -> bool:
        """更新关键词"""
        if not kwargs:
            return False
        
        set_clause = ", ".join([f"{key} = ?" for key in kwargs.keys()])
        values = list(kwargs.values()) + [keyword_id]
        
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                cursor.execute(
                    f"UPDATE keywords SET {set_clause}, updated_at = CURRENT_TIMESTAMP WHERE id = ?",
                    values
                )
                conn.commit()
                return cursor.rowcount > 0
        except Exception:
            return False
    
    def delete_keyword(self, keyword_id: int) -> bool:
        """删除关键词"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                cursor.execute("DELETE FROM keywords WHERE id = ?", (keyword_id,))
                conn.commit()
                return cursor.rowcount > 0
        except Exception:
            return False
    
    # 任务管理
    def add_task(self, name: str, keywords: List[str], config: Dict[str, Any] = None) -> int:
        """添加任务"""
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()
            cursor.execute(
                "INSERT INTO tasks (name, keywords, config) VALUES (?, ?, ?)",
                (name, json.dumps(keywords), json.dumps(config or {}))
            )
            conn.commit()
            return cursor.lastrowid
    
    def get_tasks(self) -> List[Dict[str, Any]]:
        """获取任务列表"""
        with sqlite3.connect(self.db_path) as conn:
            conn.row_factory = sqlite3.Row
            cursor = conn.cursor()
            cursor.execute("SELECT * FROM tasks ORDER BY created_at DESC")
            
            tasks = []
            for row in cursor.fetchall():
                task = dict(row)
                task['keywords'] = json.loads(task['keywords'])
                task['config'] = json.loads(task['config'] or '{}')
                tasks.append(task)
            
            return tasks

    def get_task(self, task_id: int) -> Optional[Dict[str, Any]]:
        """获取单个任务"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                conn.row_factory = sqlite3.Row
                cursor = conn.cursor()
                cursor.execute("SELECT * FROM tasks WHERE id = ?", (task_id,))

                row = cursor.fetchone()
                if row:
                    task = dict(row)
                    task['keywords'] = json.loads(task['keywords'])
                    task['config'] = json.loads(task['config'] or '{}')
                    return task
                return None

        except Exception as e:
            logger.error(f"获取任务失败: {str(e)}")
            return None

    def delete_task(self, task_id: int) -> bool:
        """删除任务"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                cursor.execute("DELETE FROM tasks WHERE id = ?", (task_id,))
                conn.commit()
                return cursor.rowcount > 0
        except Exception as e:
            logger.error(f"删除任务失败: {str(e)}")
            return False

    def has_replied_to_note(self, note_id: str) -> bool:
        """检查是否已回复过该笔记"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                cursor.execute(
                    "SELECT COUNT(*) FROM reply_records WHERE note_id = ? AND reply_status = 'success'",
                    (note_id,)
                )
                count = cursor.fetchone()[0]
                return count > 0
        except Exception as e:
            logger.error(f"检查回复记录失败: {str(e)}")
            return False

    def update_task_status(self, task_id: int, status: str) -> bool:
        """更新任务状态"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                cursor.execute(
                    "UPDATE tasks SET status = ?, updated_at = CURRENT_TIMESTAMP WHERE id = ?",
                    (status, task_id)
                )
                conn.commit()
                return cursor.rowcount > 0
        except Exception:
            return False

    def update_keyword_stats(self, keyword: str, search_count: int = 1):
        """更新关键词统计"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                # 这里可以添加关键词统计表的更新逻辑
                # 暂时只记录日志
                logger.info(f"关键词统计更新: {keyword} (+{search_count})")
        except Exception as e:
            logger.error(f"更新关键词统计失败: {str(e)}")

    # 搜索记录
    def add_search_record(self, keyword: str, note_id: str, note_title: str = None, 
                         note_author: str = None, note_url: str = None):
        """添加搜索记录"""
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()
            cursor.execute(
                "INSERT INTO search_records (keyword, note_id, note_title, note_author, note_url) VALUES (?, ?, ?, ?, ?)",
                (keyword, note_id, note_title, note_author, note_url)
            )
            conn.commit()
    
    # 回复记录
    def add_reply_record(self, note_id: str, reply_content: str, reply_status: str = 'pending'):
        """添加回复记录"""
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()
            cursor.execute(
                "INSERT INTO reply_records (note_id, reply_content, reply_status) VALUES (?, ?, ?)",
                (note_id, reply_content, reply_status)
            )
            conn.commit()
    
    def update_reply_status(self, record_id: int, status: str, error_message: str = None):
        """更新回复状态"""
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()
            cursor.execute(
                "UPDATE reply_records SET reply_status = ?, error_message = ? WHERE id = ?",
                (status, error_message, record_id)
            )
            conn.commit()
    
    # 用户会话管理
    def save_session(self, user_id: str, cookies: Dict, headers: Dict, expires_at: datetime = None):
        """保存用户会话"""
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()
            # 先禁用旧会话
            cursor.execute("UPDATE user_sessions SET is_active = 0 WHERE user_id = ?", (user_id,))
            
            # 添加新会话
            cursor.execute(
                "INSERT INTO user_sessions (user_id, cookies, headers, expires_at) VALUES (?, ?, ?, ?)",
                (user_id, json.dumps(cookies), json.dumps(headers), expires_at)
            )
            conn.commit()
    
    def get_active_session(self) -> Optional[Dict[str, Any]]:
        """获取活跃会话"""
        with sqlite3.connect(self.db_path) as conn:
            conn.row_factory = sqlite3.Row
            cursor = conn.cursor()
            cursor.execute(
                "SELECT * FROM user_sessions WHERE is_active = 1 ORDER BY created_at DESC LIMIT 1"
            )
            row = cursor.fetchone()
            if row:
                session = dict(row)
                session['cookies'] = json.loads(session['cookies'])
                session['headers'] = json.loads(session['headers'])
                return session
            return None

# 全局数据库实例
db = Database()
