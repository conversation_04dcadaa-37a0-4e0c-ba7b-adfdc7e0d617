"""
任务管理模块
"""
import threading
import time
import queue
from datetime import datetime, timedelta
from typing import List, Dict, Any, Optional, Callable

from config.settings import settings
from config.database import db
from utils.logger import logger
from core.search import search_engine
from core.reply import reply_engine
from core.auth import auth

class Task:
    """任务类"""
    
    def __init__(self, task_id: int, name: str, keywords: List[str], 
                 config: Dict[str, Any] = None):
        self.id = task_id
        self.name = name
        self.keywords = keywords
        self.config = config or {}
        self.status = "stopped"  # stopped, running, paused
        self.thread = None
        self.stop_event = threading.Event()
        self.pause_event = threading.Event()
        
        # 统计信息
        self.stats = {
            "start_time": None,
            "last_run_time": None,
            "total_searches": 0,
            "total_replies": 0,
            "success_replies": 0,
            "failed_replies": 0,
            "errors": []
        }
        
        # 回调函数
        self.status_callback = None
        self.progress_callback = None
    
    def set_callbacks(self, status_callback: Callable = None, 
                     progress_callback: Callable = None):
        """设置回调函数"""
        self.status_callback = status_callback
        self.progress_callback = progress_callback
    
    def start(self):
        """启动任务"""
        if self.status == "running":
            return False
        
        self.stop_event.clear()
        self.pause_event.clear()
        self.status = "running"
        self.stats["start_time"] = datetime.now()
        
        # 更新数据库状态
        db.update_task_status(self.id, "running")
        
        # 启动任务线程
        self.thread = threading.Thread(target=self._run_task, daemon=True)
        self.thread.start()
        
        logger.log_task_start(self.name, self.keywords)
        
        if self.status_callback:
            self.status_callback(self.id, "running", "任务已启动")
        
        return True
    
    def stop(self):
        """停止任务"""
        if self.status == "stopped":
            return False
        
        self.stop_event.set()
        self.status = "stopped"
        
        # 更新数据库状态
        db.update_task_status(self.id, "stopped")
        
        if self.thread and self.thread.is_alive():
            self.thread.join(timeout=5)
        
        logger.log_task_end(
            self.name, 
            self.stats["success_replies"], 
            self.stats["failed_replies"]
        )
        
        if self.status_callback:
            self.status_callback(self.id, "stopped", "任务已停止")
        
        return True
    
    def pause(self):
        """暂停任务"""
        if self.status != "running":
            return False
        
        self.pause_event.set()
        self.status = "paused"
        
        # 更新数据库状态
        db.update_task_status(self.id, "paused")
        
        if self.status_callback:
            self.status_callback(self.id, "paused", "任务已暂停")
        
        return True
    
    def resume(self):
        """恢复任务"""
        if self.status != "paused":
            return False
        
        self.pause_event.clear()
        self.status = "running"
        
        # 更新数据库状态
        db.update_task_status(self.id, "running")
        
        if self.status_callback:
            self.status_callback(self.id, "running", "任务已恢复")
        
        return True
    
    def _run_task(self):
        """运行任务主循环"""
        try:
            search_interval = self.config.get("search_interval", 
                                            settings.get("task.search_interval", 30))
            
            while not self.stop_event.is_set():
                # 检查暂停状态
                if self.pause_event.is_set():
                    time.sleep(1)
                    continue
                
                # 检查登录状态
                if not auth.is_logged_in():
                    self._add_error("用户未登录，任务暂停")
                    self.pause()
                    break
                
                # 执行搜索和回复
                self._execute_search_and_reply()
                
                # 更新最后运行时间
                self.stats["last_run_time"] = datetime.now()
                
                # 等待下次执行
                for _ in range(search_interval):
                    if self.stop_event.is_set() or self.pause_event.is_set():
                        break
                    time.sleep(1)
        
        except Exception as e:
            error_msg = f"任务执行异常: {str(e)}"
            self._add_error(error_msg)
            logger.error(error_msg)
            self.stop()
    
    def _execute_search_and_reply(self):
        """执行搜索和回复"""
        try:
            for keyword in self.keywords:
                if self.stop_event.is_set() or self.pause_event.is_set():
                    break
                
                # 搜索笔记
                search_result = search_engine.search_notes(keyword)
                self.stats["total_searches"] += 1
                
                if not search_result.get("success"):
                    self._add_error(f"搜索关键词 '{keyword}' 失败: {search_result.get('error')}")
                    continue
                
                notes = search_result.get("notes", [])
                if not notes:
                    continue
                
                # 过滤需要回复的笔记
                notes_to_reply = self._filter_notes_for_reply(notes)
                
                # 执行回复
                for note in notes_to_reply:
                    if self.stop_event.is_set() or self.pause_event.is_set():
                        break
                    
                    reply_result = reply_engine.reply_to_note(
                        note_id=note.get("id", ""),
                        note_title=note.get("title", ""),
                        note_content=note.get("content", "")
                    )
                    
                    self.stats["total_replies"] += 1
                    
                    if reply_result.get("success"):
                        self.stats["success_replies"] += 1
                    else:
                        self.stats["failed_replies"] += 1
                        self._add_error(f"回复失败: {reply_result.get('error')}")
                    
                    # 更新进度
                    if self.progress_callback:
                        self.progress_callback(self.id, self.stats)
                    
                    # 检查每日回复限制
                    daily_stats = reply_engine.get_daily_stats()
                    if daily_stats["remaining_replies"] <= 0:
                        self._add_error("已达到每日回复限制")
                        self.pause()
                        return
        
        except Exception as e:
            self._add_error(f"执行搜索回复异常: {str(e)}")
    
    def _filter_notes_for_reply(self, notes: List[Dict]) -> List[Dict]:
        """过滤需要回复的笔记"""
        filtered_notes = []
        
        max_replies_per_note = self.config.get("max_replies_per_note", 1)
        min_like_count = self.config.get("min_like_count", 0)
        max_like_count = self.config.get("max_like_count", float('inf'))
        
        for note in notes:
            # 检查点赞数范围
            like_count = note.get("stats", {}).get("liked_count", 0)
            if not (min_like_count <= like_count <= max_like_count):
                continue
            
            # 检查是否可以回复
            if reply_engine.can_reply(note.get("id", "")):
                filtered_notes.append(note)
                
                # 限制每次回复的笔记数量
                if len(filtered_notes) >= max_replies_per_note:
                    break
        
        return filtered_notes
    
    def _add_error(self, error_msg: str):
        """添加错误信息"""
        self.stats["errors"].append({
            "time": datetime.now().isoformat(),
            "message": error_msg
        })
        
        # 只保留最近的50个错误
        if len(self.stats["errors"]) > 50:
            self.stats["errors"] = self.stats["errors"][-50:]
    
    def get_stats(self) -> Dict[str, Any]:
        """获取任务统计信息"""
        stats = self.stats.copy()
        stats["status"] = self.status
        stats["keywords"] = self.keywords
        stats["config"] = self.config
        return stats

class TaskManager:
    """任务管理器"""
    
    def __init__(self):
        self.tasks: Dict[int, Task] = {}
        self.status_callbacks = []
        self.progress_callbacks = []
        
        # 加载已有任务
        self._load_tasks()
    
    def add_status_callback(self, callback: Callable):
        """添加状态回调"""
        self.status_callbacks.append(callback)
    
    def add_progress_callback(self, callback: Callable):
        """添加进度回调"""
        self.progress_callbacks.append(callback)
    
    def _notify_status_change(self, task_id: int, status: str, message: str):
        """通知状态变化"""
        for callback in self.status_callbacks:
            try:
                callback(task_id, status, message)
            except Exception as e:
                logger.error(f"状态回调异常: {str(e)}")
    
    def _notify_progress_update(self, task_id: int, stats: Dict):
        """通知进度更新"""
        for callback in self.progress_callbacks:
            try:
                callback(task_id, stats)
            except Exception as e:
                logger.error(f"进度回调异常: {str(e)}")
    
    def _load_tasks(self):
        """从数据库加载任务"""
        try:
            task_list = db.get_tasks()
            for task_data in task_list:
                task = Task(
                    task_id=task_data["id"],
                    name=task_data["name"],
                    keywords=task_data["keywords"],
                    config=task_data["config"]
                )
                task.set_callbacks(
                    status_callback=self._notify_status_change,
                    progress_callback=self._notify_progress_update
                )
                self.tasks[task.id] = task
            
            logger.info(f"已加载 {len(self.tasks)} 个任务")
            
        except Exception as e:
            logger.error(f"加载任务失败: {str(e)}")
    
    def create_task(self, name: str, keywords: List[str],
                   config: Dict[str, Any] = None) -> Optional[int]:
        """创建新任务"""
        try:
            # 验证参数
            if not name or not name.strip():
                raise ValueError("任务名称不能为空")

            if not keywords or len(keywords) == 0:
                raise ValueError("至少需要一个关键词")

            # 检查任务名称是否重复
            for task in self.tasks.values():
                if task.name == name.strip():
                    raise ValueError(f"任务名称 '{name}' 已存在")

            # 设置默认配置
            default_config = {
                "search_interval": 60,
                "reply_interval": 30,
                "max_daily_replies": 50,
                "max_replies_per_note": 1,
                "min_like_count": 0,
                "max_like_count": 10000,
                "enabled": True,
                "auto_start": False,
                "reply_template": "默认模板",
                "custom_reply": ""
            }

            # 合并用户配置
            final_config = {**default_config, **(config or {})}

            # 保存到数据库
            task_id = db.add_task(name.strip(), keywords, final_config)

            if not task_id:
                raise Exception("数据库保存失败")

            task = Task(
                task_id=task_id,
                name=name.strip(),
                keywords=keywords,
                config=final_config
            )
            task.set_callbacks(
                status_callback=self._notify_status_change,
                progress_callback=self._notify_progress_update
            )

            self.tasks[task_id] = task

            logger.info(f"已创建任务: {name} (ID: {task_id})")
            self._notify_status_change(task_id, "created", f"任务 '{name}' 创建成功")

            return task_id

        except Exception as e:
            logger.error(f"创建任务失败: {str(e)}")
            self._notify_status_change(0, "error", f"创建任务失败: {str(e)}")
            return None
    
    def start_task(self, task_id: int) -> bool:
        """启动任务"""
        task = self.tasks.get(task_id)
        if task:
            return task.start()
        return False
    
    def stop_task(self, task_id: int) -> bool:
        """停止任务"""
        task = self.tasks.get(task_id)
        if task:
            return task.stop()
        return False
    
    def pause_task(self, task_id: int) -> bool:
        """暂停任务"""
        task = self.tasks.get(task_id)
        if task:
            return task.pause()
        return False
    
    def resume_task(self, task_id: int) -> bool:
        """恢复任务"""
        task = self.tasks.get(task_id)
        if task:
            return task.resume()
        return False
    
    def delete_task(self, task_id: int) -> bool:
        """删除任务"""
        try:
            task = self.tasks.get(task_id)
            if task:
                task.stop()
                del self.tasks[task_id]
            
            # 从数据库删除
            # 这里需要实现数据库删除逻辑
            
            logger.info(f"已删除任务: {task_id}")
            return True
            
        except Exception as e:
            logger.error(f"删除任务失败: {str(e)}")
            return False
    
    def get_task_list(self) -> List[Dict[str, Any]]:
        """获取任务列表"""
        task_list = []
        for task in self.tasks.values():
            task_list.append(task.get_stats())
        return task_list
    
    def get_task_stats(self, task_id: int) -> Optional[Dict[str, Any]]:
        """获取任务统计"""
        task = self.tasks.get(task_id)
        if task:
            return task.get_stats()
        return None
    
    def stop_all_tasks(self):
        """停止所有任务"""
        for task in self.tasks.values():
            task.stop()
        logger.info("已停止所有任务")

# 全局任务管理器实例
task_manager = TaskManager()
