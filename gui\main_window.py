"""
主窗口界面
"""
import tkinter as tk
from tkinter import ttk, messagebox
import customtkinter as ctk
from typing import Dict, Any, List
import threading
import time

from config.settings import settings
from utils.logger import logger
from core.auth import auth
from core.task_manager import task_manager
from gui.web_login_window import WebLoginWindow
from gui.keyword_manager import KeywordManagerWindow
from gui.task_monitor import TaskMonitorWindow

# 设置CustomTkinter主题
ctk.set_appearance_mode("dark")
ctk.set_default_color_theme("blue")

class MainWindow:
    """主窗口类"""
    
    def __init__(self):
        self.root = ctk.CTk()
        self.setup_window()
        self.setup_ui()
        self.setup_callbacks()
        
        # 子窗口
        self.web_login_window = None
        self.keyword_window = None
        self.monitor_window = None
        
        # 状态变量
        self.is_logged_in = False
        self.user_info = None
        
        # 检查登录状态
        self.check_login_status()
    
    def setup_window(self):
        """设置窗口属性"""
        self.root.title(settings.get("app.name", "小红书自动回帖机器人"))
        self.root.geometry(settings.get("gui.window_size", "1200x800"))
        
        # 设置窗口图标（如果有的话）
        try:
            self.root.iconbitmap("assets/icon.ico")
        except:
            pass
        
        # 设置窗口关闭事件
        self.root.protocol("WM_DELETE_WINDOW", self.on_closing)
    
    def setup_ui(self):
        """设置用户界面"""
        # 创建主框架
        self.main_frame = ctk.CTkFrame(self.root)
        self.main_frame.pack(fill="both", expand=True, padx=10, pady=10)
        
        # 顶部工具栏
        self.create_toolbar()
        
        # 左侧面板
        self.create_left_panel()
        
        # 右侧面板
        self.create_right_panel()
        
        # 底部状态栏
        self.create_status_bar()
    
    def create_toolbar(self):
        """创建工具栏"""
        self.toolbar_frame = ctk.CTkFrame(self.main_frame)
        self.toolbar_frame.pack(fill="x", padx=5, pady=5)
        
        # 登录状态
        self.login_status_label = ctk.CTkLabel(
            self.toolbar_frame, 
            text="未登录", 
            font=ctk.CTkFont(size=14, weight="bold")
        )
        self.login_status_label.pack(side="left", padx=10, pady=10)
        
        # 登录按钮
        self.login_button = ctk.CTkButton(
            self.toolbar_frame,
            text="登录",
            command=self.show_web_login_window,
            width=80
        )
        self.login_button.pack(side="left", padx=5, pady=10)
        
        # 登出按钮
        self.logout_button = ctk.CTkButton(
            self.toolbar_frame,
            text="登出",
            command=self.logout,
            width=80,
            state="disabled"
        )
        self.logout_button.pack(side="left", padx=5, pady=10)
        
        # 右侧按钮
        self.settings_button = ctk.CTkButton(
            self.toolbar_frame,
            text="设置",
            command=self.show_settings,
            width=80
        )
        self.settings_button.pack(side="right", padx=5, pady=10)
        
        self.help_button = ctk.CTkButton(
            self.toolbar_frame,
            text="帮助",
            command=self.show_help,
            width=80
        )
        self.help_button.pack(side="right", padx=5, pady=10)
    
    def create_left_panel(self):
        """创建左侧面板"""
        # 创建左右分割的框架
        self.content_frame = ctk.CTkFrame(self.main_frame)
        self.content_frame.pack(fill="both", expand=True, padx=5, pady=5)
        
        # 左侧面板
        self.left_panel = ctk.CTkFrame(self.content_frame)
        self.left_panel.pack(side="left", fill="y", padx=5, pady=5)
        
        # 功能按钮
        self.create_function_buttons()
        
        # 关键词管理
        self.create_keyword_section()
    
    def create_function_buttons(self):
        """创建功能按钮"""
        # 标题
        title_label = ctk.CTkLabel(
            self.left_panel,
            text="功能菜单",
            font=ctk.CTkFont(size=16, weight="bold")
        )
        title_label.pack(pady=10)
        
        # 按钮列表
        buttons = [
            ("关键词管理", self.show_keyword_manager),
            ("任务监控", self.show_task_monitor),
            ("创建任务", self.create_new_task),
            ("回复模板", self.manage_reply_templates),
            ("数据统计", self.show_statistics),
            ("日志查看", self.show_logs)
        ]
        
        for text, command in buttons:
            button = ctk.CTkButton(
                self.left_panel,
                text=text,
                command=command,
                width=150,
                height=35
            )
            button.pack(pady=5, padx=10)
    
    def create_keyword_section(self):
        """创建关键词部分"""
        # 分隔线
        separator = ctk.CTkFrame(self.left_panel, height=2)
        separator.pack(fill="x", pady=10, padx=10)
        
        # 关键词标题
        keyword_title = ctk.CTkLabel(
            self.left_panel,
            text="快速关键词",
            font=ctk.CTkFont(size=14, weight="bold")
        )
        keyword_title.pack(pady=5)
        
        # 关键词输入
        self.keyword_entry = ctk.CTkEntry(
            self.left_panel,
            placeholder_text="输入关键词",
            width=150
        )
        self.keyword_entry.pack(pady=5, padx=10)
        
        # 添加按钮
        add_keyword_button = ctk.CTkButton(
            self.left_panel,
            text="添加关键词",
            command=self.add_quick_keyword,
            width=150,
            height=30
        )
        add_keyword_button.pack(pady=5, padx=10)
        
        # 关键词列表
        self.keyword_listbox = tk.Listbox(
            self.left_panel,
            height=8,
            bg="#2b2b2b",
            fg="white",
            selectbackground="#1f538d"
        )
        self.keyword_listbox.pack(pady=5, padx=10, fill="x")
        
        # 刷新关键词列表
        self.refresh_keyword_list()
    
    def create_right_panel(self):
        """创建右侧面板"""
        self.right_panel = ctk.CTkFrame(self.content_frame)
        self.right_panel.pack(side="right", fill="both", expand=True, padx=5, pady=5)
        
        # 创建选项卡
        self.notebook = ttk.Notebook(self.right_panel)
        self.notebook.pack(fill="both", expand=True, padx=10, pady=10)
        
        # 任务列表选项卡
        self.create_task_tab()
        
        # 日志选项卡
        self.create_log_tab()
        
        # 统计选项卡
        self.create_stats_tab()
    
    def create_task_tab(self):
        """创建任务选项卡"""
        self.task_frame = ctk.CTkFrame(self.notebook)
        self.notebook.add(self.task_frame, text="任务管理")
        
        # 任务列表
        self.task_tree = ttk.Treeview(
            self.task_frame,
            columns=("name", "keywords", "status", "stats"),
            show="headings",
            height=15
        )
        
        # 设置列标题
        self.task_tree.heading("name", text="任务名称")
        self.task_tree.heading("keywords", text="关键词")
        self.task_tree.heading("status", text="状态")
        self.task_tree.heading("stats", text="统计")
        
        # 设置列宽
        self.task_tree.column("name", width=150)
        self.task_tree.column("keywords", width=200)
        self.task_tree.column("status", width=100)
        self.task_tree.column("stats", width=150)
        
        self.task_tree.pack(fill="both", expand=True, padx=10, pady=10)
        
        # 任务操作按钮
        self.create_task_buttons()
        
        # 刷新任务列表
        self.refresh_task_list()
    
    def create_task_buttons(self):
        """创建任务操作按钮"""
        button_frame = ctk.CTkFrame(self.task_frame)
        button_frame.pack(fill="x", padx=10, pady=5)
        
        buttons = [
            ("启动", self.start_selected_task),
            ("停止", self.stop_selected_task),
            ("暂停", self.pause_selected_task),
            ("恢复", self.resume_selected_task),
            ("删除", self.delete_selected_task)
        ]
        
        for text, command in buttons:
            button = ctk.CTkButton(
                button_frame,
                text=text,
                command=command,
                width=80,
                height=30
            )
            button.pack(side="left", padx=5, pady=5)
    
    def create_log_tab(self):
        """创建日志选项卡"""
        self.log_frame = ctk.CTkFrame(self.notebook)
        self.notebook.add(self.log_frame, text="运行日志")
        
        # 日志文本框
        self.log_text = tk.Text(
            self.log_frame,
            bg="#2b2b2b",
            fg="white",
            font=("Consolas", 10),
            wrap="word"
        )
        self.log_text.pack(fill="both", expand=True, padx=10, pady=10)
        
        # 滚动条
        log_scrollbar = ttk.Scrollbar(self.log_text)
        log_scrollbar.pack(side="right", fill="y")
        self.log_text.config(yscrollcommand=log_scrollbar.set)
        log_scrollbar.config(command=self.log_text.yview)
    
    def create_stats_tab(self):
        """创建统计选项卡"""
        self.stats_frame = ctk.CTkFrame(self.notebook)
        self.notebook.add(self.stats_frame, text="数据统计")
        
        # 统计信息标签
        self.stats_labels = {}
        stats_info = [
            ("今日搜索次数", "daily_searches"),
            ("今日回复次数", "daily_replies"),
            ("成功回复数", "success_replies"),
            ("失败回复数", "failed_replies"),
            ("运行任务数", "running_tasks")
        ]
        
        for i, (text, key) in enumerate(stats_info):
            label = ctk.CTkLabel(
                self.stats_frame,
                text=f"{text}: 0",
                font=ctk.CTkFont(size=14)
            )
            label.pack(pady=10)
            self.stats_labels[key] = label
    
    def create_status_bar(self):
        """创建状态栏"""
        self.status_frame = ctk.CTkFrame(self.main_frame)
        self.status_frame.pack(fill="x", padx=5, pady=5)
        
        self.status_label = ctk.CTkLabel(
            self.status_frame,
            text="就绪",
            font=ctk.CTkFont(size=12)
        )
        self.status_label.pack(side="left", padx=10, pady=5)
        
        # 时间标签
        self.time_label = ctk.CTkLabel(
            self.status_frame,
            text="",
            font=ctk.CTkFont(size=12)
        )
        self.time_label.pack(side="right", padx=10, pady=5)
        
        # 启动时间更新
        self.update_time()

        # 启动定时刷新
        self.start_auto_refresh()
    
    def setup_callbacks(self):
        """设置回调函数"""
        # 任务管理器回调
        task_manager.add_status_callback(self.on_task_status_change)
        task_manager.add_progress_callback(self.on_task_progress_update)
        
        # 认证回调
        auth.set_login_callback(self.on_login_status_change)
    
    def check_login_status(self):
        """检查登录状态"""
        def check():
            try:
                session_loaded = auth.load_saved_session()

                if session_loaded:
                    logger.info("会话加载成功，尝试获取用户信息...")
                    self.is_logged_in = True

                    # 尝试获取用户信息
                    user_info = auth.get_user_info()
                    if user_info:
                        logger.info(f"获取用户信息成功: {user_info.get('nickname', 'Unknown')}")
                        self.user_info = user_info
                    else:
                        # 如果无法获取用户信息，尝试从cookie.txt获取
                        logger.warning("无法获取用户信息，尝试从cookie.txt获取...")
                        try:
                            cookie_data = auth.load_cookies_from_txt()
                            saved_user_info = cookie_data.get("user_info", {})
                            if saved_user_info.get("nickname"):
                                logger.info(f"从cookie.txt获取用户信息: {saved_user_info.get('nickname')}")
                                self.user_info = saved_user_info
                            else:
                                self.user_info = {"nickname": "已登录用户"}
                        except:
                            self.user_info = {"nickname": "已登录用户"}

                    self.root.after(0, self.update_login_ui)
                else:
                    logger.info("没有有效的登录会话")
                    self.is_logged_in = False
                    self.user_info = None
                    self.root.after(0, self.update_login_ui)

            except Exception as e:
                logger.error(f"检查登录状态失败: {str(e)}")
                self.is_logged_in = False
                self.user_info = None
                self.root.after(0, self.update_login_ui)

        threading.Thread(target=check, daemon=True).start()
    
    def update_login_ui(self):
        """更新登录界面"""
        if self.is_logged_in and self.user_info:
            nickname = self.user_info.get("nickname", "用户")
            self.login_status_label.configure(text=f"已登录: {nickname}")
            self.login_button.configure(state="disabled")
            self.logout_button.configure(state="normal")
        else:
            self.login_status_label.configure(text="未登录")
            self.login_button.configure(state="normal")
            self.logout_button.configure(state="disabled")
    
    def update_time(self):
        """更新时间显示"""
        current_time = time.strftime("%Y-%m-%d %H:%M:%S")
        self.time_label.configure(text=current_time)
        self.root.after(1000, self.update_time)

    def start_auto_refresh(self):
        """启动自动刷新"""
        self.auto_refresh_tasks()

    def auto_refresh_tasks(self):
        """自动刷新任务列表"""
        try:
            # 刷新任务列表
            self.refresh_task_list()

            # 更新统计信息
            self.update_stats()

        except Exception as e:
            logger.error(f"自动刷新失败: {str(e)}")

        # 每10秒刷新一次
        self.root.after(10000, self.auto_refresh_tasks)
    
    # 事件处理方法

    def show_web_login_window(self):
        """显示Web浏览器登录窗口"""
        if not self.web_login_window or not self.web_login_window.window.winfo_exists():
            self.web_login_window = WebLoginWindow(self.root, self.on_login_success)
    
    def on_login_success(self, user_info):
        """登录成功回调"""
        self.is_logged_in = True
        self.user_info = user_info
        self.update_login_ui()
        self.update_status("登录成功")
    
    def logout(self):
        """登出"""
        auth.logout()
        self.is_logged_in = False
        self.user_info = None
        self.update_login_ui()
        self.update_status("已登出")
    
    def on_login_status_change(self, status, message):
        """登录状态变化回调"""
        self.update_status(message)
    
    def on_task_status_change(self, task_id, status, message):
        """任务状态变化回调"""
        self.root.after(0, lambda: self.refresh_task_list())
        self.update_status(f"任务 {task_id}: {message}")
    
    def on_task_progress_update(self, task_id, stats):
        """任务进度更新回调"""
        self.root.after(0, lambda: self.update_stats())
    
    def update_status(self, message):
        """更新状态栏"""
        self.status_label.configure(text=message)
        logger.info(message)
    
    def refresh_task_list(self):
        """刷新任务列表"""
        try:
            # 清空现有项目
            for item in self.task_tree.get_children():
                self.task_tree.delete(item)

            # 添加任务
            tasks = task_manager.get_task_list()
            for task in tasks:
                task_id = task.get("id", 0)
                task_name = task.get("name", "")
                keywords = task.get("keywords", [])
                keywords_str = ", ".join(keywords)[:50]

                # 获取任务状态
                status = task.get("status", "stopped")

                # 获取运行时统计
                runtime_stats = task.get("runtime_stats", {})
                if runtime_stats:
                    search_count = runtime_stats.get("search_count", 0)
                    reply_count = runtime_stats.get("reply_count", 0)
                    error_count = runtime_stats.get("error_count", 0)
                    stats_str = f"搜索:{search_count} 回复:{reply_count} 错误:{error_count}"
                else:
                    # 使用历史统计
                    success_replies = task.get("success_replies", 0)
                    failed_replies = task.get("failed_replies", 0)
                    stats_str = f"成功:{success_replies} 失败:{failed_replies}"

                # 插入任务项，使用task_id作为标识
                item_id = self.task_tree.insert("", "end", values=(
                    task_name,
                    keywords_str,
                    status,
                    stats_str
                ))

                # 将task_id存储在item的tags中，方便后续获取
                self.task_tree.set(item_id, "#0", str(task_id))

        except Exception as e:
            logger.error(f"刷新任务列表失败: {str(e)}")
            self.update_status(f"刷新任务列表失败: {str(e)}")
    
    def refresh_keyword_list(self):
        """刷新关键词列表"""
        # 这里应该从数据库获取关键词
        pass
    
    def update_stats(self):
        """更新统计信息"""
        # 这里应该获取实际的统计数据
        pass
    
    # 功能方法（占位符）
    def show_keyword_manager(self):
        """显示关键词管理器"""
        if not self.keyword_window or not self.keyword_window.window.winfo_exists():
            self.keyword_window = KeywordManagerWindow(self.root)
    
    def show_task_monitor(self):
        """显示任务监控"""
        if not self.monitor_window or not self.monitor_window.window.winfo_exists():
            self.monitor_window = TaskMonitorWindow(self.root)
    
    def create_new_task(self):
        """创建新任务"""
        if not self.is_logged_in:
            messagebox.showwarning("提示", "请先登录后再创建任务")
            return

        try:
            from gui.task_creator import TaskCreatorWindow
            TaskCreatorWindow(self.root)
        except Exception as e:
            messagebox.showerror("错误", f"打开任务创建窗口失败: {str(e)}")
    
    def manage_reply_templates(self):
        """管理回复模板"""
        messagebox.showinfo("提示", "回复模板管理功能开发中...")
    
    def show_statistics(self):
        """显示统计"""
        messagebox.showinfo("提示", "数据统计功能开发中...")
    
    def show_logs(self):
        """显示日志"""
        messagebox.showinfo("提示", "日志查看功能开发中...")
    
    def show_settings(self):
        """显示设置"""
        messagebox.showinfo("提示", "设置功能开发中...")
    
    def show_help(self):
        """显示帮助"""
        help_text = """小红书自动回帖机器人 v1.0.0

🎯 功能说明:
1. Web浏览器登录小红书账号
2. 管理搜索关键词
3. 创建自动回复任务
4. 监控任务运行状态

🌐 登录方式:
• Web浏览器登录：在浏览器中手动登录，自动获取Cookie
• 支持所有官方登录方式（手机验证码、邮箱、扫码等）

🔧 使用步骤:
1. 点击"登录"按钮
2. 在自动打开的浏览器中完成登录
3. 等待程序自动检测并保存Cookie
4. 在"关键词管理"中添加搜索关键词
5. 创建任务并配置参数
6. 在"任务监控"中查看运行状态

⏰ 登录时间:
• 前2分钟：纯等待时间，不进行检测
• 2-30分钟：自动检测登录状态
• 充足时间完成各种登录操作

💡 登录优势:
• 支持所有官方登录方式
• 自动获取真实Cookie
• 安全可靠，无需API逆向
• 多重验证机制，避免误判

⚠️ 注意事项:
• 需要安装Chrome浏览器
• 请遵守平台使用规则
• 合理设置回复频率
• 确保回复内容有意义"""

        messagebox.showinfo("帮助", help_text)
    
    def add_quick_keyword(self):
        """添加快速关键词"""
        keyword = self.keyword_entry.get().strip()
        if keyword:
            # 这里应该添加到数据库
            self.keyword_entry.delete(0, "end")
            self.refresh_keyword_list()
            self.update_status(f"已添加关键词: {keyword}")
    
    # 任务操作方法
    def start_selected_task(self):
        """启动选中的任务"""
        try:
            selection = self.task_tree.selection()
            if not selection:
                messagebox.showwarning("提示", "请先选择要启动的任务")
                return

            # 检查登录状态
            if not self.is_logged_in:
                messagebox.showwarning("提示", "请先登录后再启动任务")
                return

            # 获取选中的任务信息
            item = selection[0]
            task_values = self.task_tree.item(item, "values")
            task_name = task_values[0]

            # 获取任务ID（需要从任务列表中查找）
            task_id = self._get_task_id_by_name(task_name)
            if not task_id:
                messagebox.showerror("错误", f"找不到任务: {task_name}")
                return

            # 确认启动
            result = messagebox.askyesno("确认启动",
                f"确定要启动任务 '{task_name}' 吗？\n\n任务启动后将自动搜索和回复笔记。")

            if result:
                # 启动任务
                success = task_manager.start_task(task_id)

                if success:
                    messagebox.showinfo("成功", f"任务 '{task_name}' 启动成功！\n\n可以在任务监控中查看运行状态。")
                    self.refresh_task_list()
                else:
                    messagebox.showerror("失败", f"任务 '{task_name}' 启动失败！\n\n请检查任务配置和网络连接。")

        except Exception as e:
            logger.error(f"启动任务失败: {str(e)}")
            messagebox.showerror("错误", f"启动任务失败: {str(e)}")

    def stop_selected_task(self):
        """停止选中的任务"""
        try:
            selection = self.task_tree.selection()
            if not selection:
                messagebox.showwarning("提示", "请先选择要停止的任务")
                return

            # 获取选中的任务信息
            item = selection[0]
            task_values = self.task_tree.item(item, "values")
            task_name = task_values[0]

            # 获取任务ID
            task_id = self._get_task_id_by_name(task_name)
            if not task_id:
                messagebox.showerror("错误", f"找不到任务: {task_name}")
                return

            # 确认停止
            result = messagebox.askyesno("确认停止", f"确定要停止任务 '{task_name}' 吗？")

            if result:
                # 停止任务
                success = task_manager.stop_task(task_id)

                if success:
                    messagebox.showinfo("成功", f"任务 '{task_name}' 已停止。")
                    self.refresh_task_list()
                else:
                    messagebox.showwarning("提示", f"任务 '{task_name}' 可能未在运行。")

        except Exception as e:
            logger.error(f"停止任务失败: {str(e)}")
            messagebox.showerror("错误", f"停止任务失败: {str(e)}")

    def pause_selected_task(self):
        """暂停选中的任务"""
        try:
            selection = self.task_tree.selection()
            if not selection:
                messagebox.showwarning("提示", "请先选择要暂停的任务")
                return

            # 获取选中的任务信息
            item = selection[0]
            task_values = self.task_tree.item(item, "values")
            task_name = task_values[0]

            # 获取任务ID
            task_id = self._get_task_id_by_name(task_name)
            if not task_id:
                messagebox.showerror("错误", f"找不到任务: {task_name}")
                return

            # 暂停任务
            success = task_manager.pause_task(task_id)

            if success:
                messagebox.showinfo("成功", f"任务 '{task_name}' 已暂停。")
                self.refresh_task_list()
            else:
                messagebox.showwarning("提示", f"任务 '{task_name}' 暂停失败或未在运行。")

        except Exception as e:
            logger.error(f"暂停任务失败: {str(e)}")
            messagebox.showerror("错误", f"暂停任务失败: {str(e)}")

    def resume_selected_task(self):
        """恢复选中的任务"""
        try:
            selection = self.task_tree.selection()
            if not selection:
                messagebox.showwarning("提示", "请先选择要恢复的任务")
                return

            # 获取选中的任务信息
            item = selection[0]
            task_values = self.task_tree.item(item, "values")
            task_name = task_values[0]

            # 获取任务ID
            task_id = self._get_task_id_by_name(task_name)
            if not task_id:
                messagebox.showerror("错误", f"找不到任务: {task_name}")
                return

            # 恢复任务
            success = task_manager.resume_task(task_id)

            if success:
                messagebox.showinfo("成功", f"任务 '{task_name}' 已恢复。")
                self.refresh_task_list()
            else:
                messagebox.showwarning("提示", f"任务 '{task_name}' 恢复失败或未处于暂停状态。")

        except Exception as e:
            logger.error(f"恢复任务失败: {str(e)}")
            messagebox.showerror("错误", f"恢复任务失败: {str(e)}")

    def delete_selected_task(self):
        """删除选中的任务"""
        try:
            selection = self.task_tree.selection()
            if not selection:
                messagebox.showwarning("提示", "请先选择要删除的任务")
                return

            # 获取选中的任务信息
            item = selection[0]
            task_values = self.task_tree.item(item, "values")
            task_name = task_values[0]

            # 获取任务ID
            task_id = self._get_task_id_by_name(task_name)
            if not task_id:
                messagebox.showerror("错误", f"找不到任务: {task_name}")
                return

            # 确认删除
            result = messagebox.askyesno("确认删除",
                f"确定要删除任务 '{task_name}' 吗？\n\n删除后将无法恢复，相关的回复记录也会被清除。")

            if result:
                # 删除任务
                success = task_manager.delete_task(task_id)

                if success:
                    messagebox.showinfo("成功", f"任务 '{task_name}' 已删除。")
                    self.refresh_task_list()
                else:
                    messagebox.showerror("失败", f"任务 '{task_name}' 删除失败。")

        except Exception as e:
            logger.error(f"删除任务失败: {str(e)}")
            messagebox.showerror("错误", f"删除任务失败: {str(e)}")

    def _get_task_id_by_name(self, task_name: str) -> int:
        """根据任务名称获取任务ID"""
        try:
            # 从选中的项目中直接获取task_id
            selection = self.task_tree.selection()
            if selection:
                item = selection[0]
                task_id_str = self.task_tree.set(item, "#0")
                if task_id_str and task_id_str.isdigit():
                    return int(task_id_str)

            # 备用方法：从任务列表中查找
            tasks = task_manager.get_task_list()
            for task in tasks:
                if task.get("name") == task_name:
                    return task.get("id")
            return None
        except Exception as e:
            logger.error(f"获取任务ID失败: {str(e)}")
            return None

    def on_closing(self):
        """窗口关闭事件"""
        if messagebox.askokcancel("退出", "确定要退出程序吗？"):
            # 停止所有任务
            task_manager.stop_all_tasks()
            self.root.destroy()
    
    def run(self):
        """运行主窗口"""
        self.root.mainloop()
