"""
基本功能测试脚本
"""
import sys
import os
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def test_imports():
    """测试模块导入"""
    print("测试模块导入...")
    
    try:
        from config.settings import settings
        print("✓ 配置模块导入成功")
    except Exception as e:
        print(f"✗ 配置模块导入失败: {e}")
        return False
    
    try:
        from config.database import db
        print("✓ 数据库模块导入成功")
    except Exception as e:
        print(f"✗ 数据库模块导入失败: {e}")
        return False
    
    try:
        from utils.logger import logger
        print("✓ 日志模块导入成功")
    except Exception as e:
        print(f"✗ 日志模块导入失败: {e}")
        return False
    
    try:
        from core.auth import auth
        print("✓ 认证模块导入成功")
    except Exception as e:
        print(f"✗ 认证模块导入失败: {e}")
        return False
    
    try:
        from core.search import search_engine
        print("✓ 搜索模块导入成功")
    except Exception as e:
        print(f"✗ 搜索模块导入失败: {e}")
        return False
    
    try:
        from core.reply import reply_engine
        print("✓ 回复模块导入成功")
    except Exception as e:
        print(f"✗ 回复模块导入失败: {e}")
        return False
    
    try:
        from core.task_manager import task_manager
        print("✓ 任务管理模块导入成功")
    except Exception as e:
        print(f"✗ 任务管理模块导入失败: {e}")
        return False
    
    return True

def test_database():
    """测试数据库功能"""
    print("\n测试数据库功能...")
    
    try:
        from config.database import db
        
        # 测试数据库初始化
        db.init_database()
        print("✓ 数据库初始化成功")
        
        # 测试添加关键词
        test_keyword = "测试关键词"
        if db.add_keyword(test_keyword):
            print("✓ 添加关键词成功")
        else:
            print("✓ 关键词已存在（正常）")
        
        # 测试获取关键词
        keywords = db.get_keywords()
        print(f"✓ 获取关键词成功，共 {len(keywords)} 个")
        
        return True
        
    except Exception as e:
        print(f"✗ 数据库测试失败: {e}")
        return False

def test_config():
    """测试配置功能"""
    print("\n测试配置功能...")
    
    try:
        from config.settings import settings
        
        # 测试获取配置
        app_name = settings.get("app.name")
        print(f"✓ 应用名称: {app_name}")
        
        # 测试设置配置
        settings.set("test.value", "test_data")
        test_value = settings.get("test.value")
        
        if test_value == "test_data":
            print("✓ 配置读写功能正常")
        else:
            print("✗ 配置读写功能异常")
            return False
        
        return True
        
    except Exception as e:
        print(f"✗ 配置测试失败: {e}")
        return False

def test_logger():
    """测试日志功能"""
    print("\n测试日志功能...")
    
    try:
        from utils.logger import logger
        
        # 测试各级别日志
        logger.debug("这是调试日志")
        logger.info("这是信息日志")
        logger.warning("这是警告日志")
        logger.error("这是错误日志")
        
        print("✓ 日志功能正常")
        return True
        
    except Exception as e:
        print(f"✗ 日志测试失败: {e}")
        return False

def test_crypto():
    """测试加密功能"""
    print("\n测试加密功能...")
    
    try:
        from utils.crypto import crypto, cookie_manager
        
        # 测试签名生成
        headers = crypto.sign_request("GET", "https://example.com")
        if headers and "X-t" in headers:
            print("✓ 签名生成功能正常")
        else:
            print("✗ 签名生成功能异常")
            return False
        
        # 测试Cookie管理
        test_cookies = {"test": "value"}
        cookie_manager.save_cookies(test_cookies, "test_user")
        loaded_cookies = cookie_manager.load_cookies()
        
        if loaded_cookies.get("test") == "value":
            print("✓ Cookie管理功能正常")
        else:
            print("✗ Cookie管理功能异常")
            return False
        
        return True
        
    except Exception as e:
        print(f"✗ 加密功能测试失败: {e}")
        return False

def test_helpers():
    """测试辅助函数"""
    print("\n测试辅助函数...")
    
    try:
        from utils.helpers import is_valid_keyword, clean_text, generate_reply_content
        
        # 测试关键词验证
        if is_valid_keyword("测试关键词") and not is_valid_keyword(""):
            print("✓ 关键词验证功能正常")
        else:
            print("✗ 关键词验证功能异常")
            return False
        
        # 测试文本清理
        cleaned = clean_text("  测试文本  \n\n  ")
        if cleaned == "测试文本":
            print("✓ 文本清理功能正常")
        else:
            print("✗ 文本清理功能异常")
            return False
        
        # 测试回复生成
        templates = ["很棒的分享！", "学到了！"]
        reply = generate_reply_content(templates)
        if reply in templates:
            print("✓ 回复生成功能正常")
        else:
            print("✗ 回复生成功能异常")
            return False
        
        return True
        
    except Exception as e:
        print(f"✗ 辅助函数测试失败: {e}")
        return False

def test_gui_imports():
    """测试GUI模块导入"""
    print("\n测试GUI模块导入...")
    
    try:
        import customtkinter as ctk
        print("✓ CustomTkinter导入成功")
    except Exception as e:
        print(f"✗ CustomTkinter导入失败: {e}")
        return False
    
    try:
        from PIL import Image, ImageTk
        print("✓ Pillow导入成功")
    except Exception as e:
        print(f"✗ Pillow导入失败: {e}")
        return False
    
    try:
        import qrcode
        print("✓ QRCode导入成功")
    except Exception as e:
        print(f"✗ QRCode导入失败: {e}")
        return False
    
    return True

def main():
    """主测试函数"""
    print("=" * 50)
    print("小红书自动回帖机器人 - 基本功能测试")
    print("=" * 50)
    
    tests = [
        ("模块导入", test_imports),
        ("数据库功能", test_database),
        ("配置功能", test_config),
        ("日志功能", test_logger),
        ("加密功能", test_crypto),
        ("辅助函数", test_helpers),
        ("GUI模块", test_gui_imports)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        try:
            if test_func():
                passed += 1
            else:
                print(f"✗ {test_name} 测试失败")
        except Exception as e:
            print(f"✗ {test_name} 测试异常: {e}")
    
    print("\n" + "=" * 50)
    print(f"测试完成: {passed}/{total} 项测试通过")
    
    if passed == total:
        print("🎉 所有测试通过！程序基本功能正常。")
        return True
    else:
        print("❌ 部分测试失败，请检查相关模块。")
        return False

if __name__ == "__main__":
    success = main()
    input("\n按回车键退出...")
    sys.exit(0 if success else 1)
