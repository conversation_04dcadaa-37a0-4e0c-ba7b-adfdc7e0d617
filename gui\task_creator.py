"""
任务创建窗口
"""
import tkinter as tk
from tkinter import ttk, messagebox
import customtkinter as ctk

from config.database import db
from core.task_manager import task_manager
from utils.logger import logger

class TaskCreatorWindow:
    """任务创建窗口类"""
    
    def __init__(self, parent):
        self.parent = parent
        
        # 创建窗口
        self.window = ctk.CTkToplevel(parent)
        self.setup_window()
        self.setup_ui()
        
        # 加载数据
        self.load_keywords()
        self.load_templates()
    
    def setup_window(self):
        """设置窗口属性"""
        self.window.title("创建新任务")
        self.window.geometry("700x800")
        
        # 设置为模态窗口
        self.window.transient(self.parent)
        self.window.grab_set()
        
        # 居中显示
        self.center_window()
    
    def center_window(self):
        """窗口居中"""
        self.window.update_idletasks()
        x = (self.window.winfo_screenwidth() // 2) - (700 // 2)
        y = (self.window.winfo_screenheight() // 2) - (800 // 2)
        self.window.geometry(f"700x800+{x}+{y}")
    
    def setup_ui(self):
        """设置用户界面"""
        # 主框架
        main_frame = ctk.CTkFrame(self.window)
        main_frame.pack(fill="both", expand=True, padx=20, pady=20)
        
        # 标题
        title_label = ctk.CTkLabel(
            main_frame,
            text="创建新任务",
            font=ctk.CTkFont(size=20, weight="bold")
        )
        title_label.pack(pady=10)
        
        # 创建各个配置部分
        self.create_basic_config(main_frame)
        self.create_keyword_config(main_frame)
        self.create_reply_config(main_frame)
        self.create_advanced_config(main_frame)
        self.create_buttons(main_frame)
    
    def create_basic_config(self, parent):
        """创建基本配置"""
        basic_frame = ctk.CTkFrame(parent)
        basic_frame.pack(fill="x", pady=10)
        
        # 标题
        basic_title = ctk.CTkLabel(
            basic_frame,
            text="基本配置",
            font=ctk.CTkFont(size=16, weight="bold")
        )
        basic_title.pack(anchor="w", padx=10, pady=5)
        
        # 任务名称
        name_frame = ctk.CTkFrame(basic_frame)
        name_frame.pack(fill="x", padx=10, pady=5)
        
        name_label = ctk.CTkLabel(name_frame, text="任务名称:")
        name_label.pack(side="left", padx=5)
        
        self.task_name_entry = ctk.CTkEntry(
            name_frame,
            placeholder_text="输入任务名称",
            width=300
        )
        self.task_name_entry.pack(side="left", padx=5)
        
        # 任务描述
        desc_frame = ctk.CTkFrame(basic_frame)
        desc_frame.pack(fill="x", padx=10, pady=5)
        
        desc_label = ctk.CTkLabel(desc_frame, text="任务描述:")
        desc_label.pack(anchor="w", padx=5)
        
        self.task_desc_text = ctk.CTkTextbox(
            desc_frame,
            height=60,
            placeholder_text="输入任务描述（可选）"
        )
        self.task_desc_text.pack(fill="x", padx=5, pady=5)
    
    def create_keyword_config(self, parent):
        """创建关键词配置"""
        keyword_frame = ctk.CTkFrame(parent)
        keyword_frame.pack(fill="x", pady=10)
        
        # 标题
        keyword_title = ctk.CTkLabel(
            keyword_frame,
            text="关键词配置",
            font=ctk.CTkFont(size=16, weight="bold")
        )
        keyword_title.pack(anchor="w", padx=10, pady=5)
        
        # 关键词选择
        select_frame = ctk.CTkFrame(keyword_frame)
        select_frame.pack(fill="both", expand=True, padx=10, pady=5)
        
        # 可用关键词列表
        available_label = ctk.CTkLabel(select_frame, text="可用关键词:")
        available_label.grid(row=0, column=0, sticky="w", padx=5, pady=5)
        
        self.available_listbox = tk.Listbox(
            select_frame,
            height=6,
            bg="#2b2b2b",
            fg="white",
            selectbackground="#1f538d",
            selectmode=tk.MULTIPLE
        )
        self.available_listbox.grid(row=1, column=0, sticky="ew", padx=5, pady=5)
        
        # 中间按钮
        button_frame = ctk.CTkFrame(select_frame)
        button_frame.grid(row=1, column=1, padx=10)
        
        add_button = ctk.CTkButton(
            button_frame,
            text="添加 →",
            command=self.add_keywords,
            width=80,
            height=30
        )
        add_button.pack(pady=5)
        
        remove_button = ctk.CTkButton(
            button_frame,
            text="← 移除",
            command=self.remove_keywords,
            width=80,
            height=30
        )
        remove_button.pack(pady=5)
        
        # 已选关键词列表
        selected_label = ctk.CTkLabel(select_frame, text="已选关键词:")
        selected_label.grid(row=0, column=2, sticky="w", padx=5, pady=5)
        
        self.selected_listbox = tk.Listbox(
            select_frame,
            height=6,
            bg="#2b2b2b",
            fg="white",
            selectbackground="#1f538d",
            selectmode=tk.MULTIPLE
        )
        self.selected_listbox.grid(row=1, column=2, sticky="ew", padx=5, pady=5)
        
        # 配置网格权重
        select_frame.grid_columnconfigure(0, weight=1)
        select_frame.grid_columnconfigure(2, weight=1)
    
    def create_reply_config(self, parent):
        """创建回复配置"""
        reply_frame = ctk.CTkFrame(parent)
        reply_frame.pack(fill="x", pady=10)
        
        # 标题
        reply_title = ctk.CTkLabel(
            reply_frame,
            text="回复配置",
            font=ctk.CTkFont(size=16, weight="bold")
        )
        reply_title.pack(anchor="w", padx=10, pady=5)
        
        # 回复模板选择
        template_frame = ctk.CTkFrame(reply_frame)
        template_frame.pack(fill="x", padx=10, pady=5)
        
        template_label = ctk.CTkLabel(template_frame, text="回复模板:")
        template_label.pack(anchor="w", padx=5)
        
        self.template_combobox = ctk.CTkComboBox(
            template_frame,
            values=["加载中..."],
            width=400
        )
        self.template_combobox.pack(anchor="w", padx=5, pady=5)
        
        # 自定义回复
        custom_frame = ctk.CTkFrame(reply_frame)
        custom_frame.pack(fill="x", padx=10, pady=5)
        
        custom_label = ctk.CTkLabel(custom_frame, text="自定义回复内容:")
        custom_label.pack(anchor="w", padx=5)
        
        self.custom_reply_text = ctk.CTkTextbox(
            custom_frame,
            height=80,
            placeholder_text="输入自定义回复内容（可选，支持变量：{title}, {author}）"
        )
        self.custom_reply_text.pack(fill="x", padx=5, pady=5)
    
    def create_advanced_config(self, parent):
        """创建高级配置"""
        advanced_frame = ctk.CTkFrame(parent)
        advanced_frame.pack(fill="x", pady=10)
        
        # 标题
        advanced_title = ctk.CTkLabel(
            advanced_frame,
            text="高级配置",
            font=ctk.CTkFont(size=16, weight="bold")
        )
        advanced_title.pack(anchor="w", padx=10, pady=5)
        
        # 配置网格
        config_grid = ctk.CTkFrame(advanced_frame)
        config_grid.pack(fill="x", padx=10, pady=5)
        
        # 搜索间隔
        search_label = ctk.CTkLabel(config_grid, text="搜索间隔(秒):")
        search_label.grid(row=0, column=0, sticky="w", padx=5, pady=5)
        
        self.search_interval_entry = ctk.CTkEntry(config_grid, width=100)
        self.search_interval_entry.insert(0, "60")
        self.search_interval_entry.grid(row=0, column=1, padx=5, pady=5)
        
        # 回复间隔
        reply_label = ctk.CTkLabel(config_grid, text="回复间隔(秒):")
        reply_label.grid(row=0, column=2, sticky="w", padx=5, pady=5)
        
        self.reply_interval_entry = ctk.CTkEntry(config_grid, width=100)
        self.reply_interval_entry.insert(0, "30")
        self.reply_interval_entry.grid(row=0, column=3, padx=5, pady=5)
        
        # 每日最大回复数
        daily_label = ctk.CTkLabel(config_grid, text="每日最大回复数:")
        daily_label.grid(row=1, column=0, sticky="w", padx=5, pady=5)
        
        self.daily_max_entry = ctk.CTkEntry(config_grid, width=100)
        self.daily_max_entry.insert(0, "50")
        self.daily_max_entry.grid(row=1, column=1, padx=5, pady=5)
        
        # 每个笔记最大回复数
        note_label = ctk.CTkLabel(config_grid, text="每个笔记最大回复数:")
        note_label.grid(row=1, column=2, sticky="w", padx=5, pady=5)
        
        self.note_max_entry = ctk.CTkEntry(config_grid, width=100)
        self.note_max_entry.insert(0, "1")
        self.note_max_entry.grid(row=1, column=3, padx=5, pady=5)
        
        # 点赞数范围
        like_min_label = ctk.CTkLabel(config_grid, text="最小点赞数:")
        like_min_label.grid(row=2, column=0, sticky="w", padx=5, pady=5)
        
        self.like_min_entry = ctk.CTkEntry(config_grid, width=100)
        self.like_min_entry.insert(0, "0")
        self.like_min_entry.grid(row=2, column=1, padx=5, pady=5)
        
        like_max_label = ctk.CTkLabel(config_grid, text="最大点赞数:")
        like_max_label.grid(row=2, column=2, sticky="w", padx=5, pady=5)
        
        self.like_max_entry = ctk.CTkEntry(config_grid, width=100)
        self.like_max_entry.insert(0, "10000")
        self.like_max_entry.grid(row=2, column=3, padx=5, pady=5)
    
    def create_buttons(self, parent):
        """创建按钮"""
        button_frame = ctk.CTkFrame(parent)
        button_frame.pack(fill="x", pady=20)
        
        # 创建任务按钮
        create_button = ctk.CTkButton(
            button_frame,
            text="创建任务",
            command=self.create_task,
            width=120,
            height=35
        )
        create_button.pack(side="left", padx=10)
        
        # 预览配置按钮
        preview_button = ctk.CTkButton(
            button_frame,
            text="预览配置",
            command=self.preview_config,
            width=120,
            height=35
        )
        preview_button.pack(side="left", padx=10)
        
        # 取消按钮
        cancel_button = ctk.CTkButton(
            button_frame,
            text="取消",
            command=self.window.destroy,
            width=120,
            height=35
        )
        cancel_button.pack(side="right", padx=10)
    
    def load_keywords(self):
        """加载关键词列表"""
        try:
            keywords = db.get_keywords(enabled_only=True)
            self.available_listbox.delete(0, tk.END)
            
            for keyword in keywords:
                self.available_listbox.insert(tk.END, keyword.get("keyword", ""))
                
        except Exception as e:
            logger.error(f"加载关键词失败: {str(e)}")
            messagebox.showerror("错误", f"加载关键词失败: {str(e)}")
    
    def load_templates(self):
        """加载回复模板"""
        try:
            from core.reply import reply_engine
            templates = reply_engine.get_reply_templates()
            
            if templates:
                self.template_combobox.configure(values=templates)
                self.template_combobox.set(templates[0])
            else:
                self.template_combobox.configure(values=["暂无模板"])
                self.template_combobox.set("暂无模板")
                
        except Exception as e:
            logger.error(f"加载回复模板失败: {str(e)}")
    
    def add_keywords(self):
        """添加关键词到已选列表"""
        selection = self.available_listbox.curselection()
        for index in reversed(selection):  # 从后往前删除，避免索引变化
            keyword = self.available_listbox.get(index)
            self.selected_listbox.insert(tk.END, keyword)
            self.available_listbox.delete(index)
    
    def remove_keywords(self):
        """从已选列表移除关键词"""
        selection = self.selected_listbox.curselection()
        for index in reversed(selection):
            keyword = self.selected_listbox.get(index)
            self.available_listbox.insert(tk.END, keyword)
            self.selected_listbox.delete(index)
    
    def preview_config(self):
        """预览任务配置"""
        try:
            config = self.get_task_config()
            
            preview_text = f"""任务配置预览:

任务名称: {config['name']}
任务描述: {config['description']}

关键词: {', '.join(config['keywords'])}

回复配置:
- 模板: {config['reply_template']}
- 自定义内容: {config['custom_reply'][:50]}...

高级配置:
- 搜索间隔: {config['search_interval']}秒
- 回复间隔: {config['reply_interval']}秒
- 每日最大回复数: {config['max_daily_replies']}
- 每个笔记最大回复数: {config['max_replies_per_note']}
- 点赞数范围: {config['min_like_count']} - {config['max_like_count']}"""
            
            messagebox.showinfo("任务配置预览", preview_text)
            
        except Exception as e:
            messagebox.showerror("错误", f"预览配置失败: {str(e)}")
    
    def get_task_config(self):
        """获取任务配置"""
        # 获取已选关键词
        keywords = []
        for i in range(self.selected_listbox.size()):
            keywords.append(self.selected_listbox.get(i))
        
        if not keywords:
            raise ValueError("请至少选择一个关键词")
        
        # 获取任务名称
        task_name = self.task_name_entry.get().strip()
        if not task_name:
            raise ValueError("请输入任务名称")
        
        # 获取配置参数
        try:
            search_interval = int(self.search_interval_entry.get())
            reply_interval = int(self.reply_interval_entry.get())
            max_daily_replies = int(self.daily_max_entry.get())
            max_replies_per_note = int(self.note_max_entry.get())
            min_like_count = int(self.like_min_entry.get())
            max_like_count = int(self.like_max_entry.get())
        except ValueError:
            raise ValueError("请输入有效的数字配置")
        
        return {
            "name": task_name,
            "description": self.task_desc_text.get("1.0", "end-1c").strip(),
            "keywords": keywords,
            "reply_template": self.template_combobox.get(),
            "custom_reply": self.custom_reply_text.get("1.0", "end-1c").strip(),
            "search_interval": search_interval,
            "reply_interval": reply_interval,
            "max_daily_replies": max_daily_replies,
            "max_replies_per_note": max_replies_per_note,
            "min_like_count": min_like_count,
            "max_like_count": max_like_count
        }
    
    def create_task(self):
        """创建任务"""
        try:
            config = self.get_task_config()
            
            # 创建任务
            task_id = task_manager.create_task(
                name=config["name"],
                keywords=config["keywords"],
                config={
                    "description": config["description"],
                    "reply_template": config["reply_template"],
                    "custom_reply": config["custom_reply"],
                    "search_interval": config["search_interval"],
                    "reply_interval": config["reply_interval"],
                    "max_daily_replies": config["max_daily_replies"],
                    "max_replies_per_note": config["max_replies_per_note"],
                    "min_like_count": config["min_like_count"],
                    "max_like_count": config["max_like_count"]
                }
            )
            
            if task_id:
                messagebox.showinfo("成功", f"任务 '{config['name']}' 创建成功！\n任务ID: {task_id}")
                self.window.destroy()
            else:
                messagebox.showerror("错误", "任务创建失败")
                
        except Exception as e:
            messagebox.showerror("错误", f"创建任务失败: {str(e)}")
            logger.error(f"创建任务失败: {str(e)}")
