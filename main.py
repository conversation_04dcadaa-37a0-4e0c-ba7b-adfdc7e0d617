"""
小红书自动回帖机器人 - 主程序入口
"""
import sys
import os
import traceback
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

try:
    # 导入必要的模块
    from config.settings import settings
    from config.database import db
    from utils.logger import logger
    from gui.main_window import MainWindow
    
    def check_dependencies():
        """检查依赖包"""
        required_packages = [
            'requests',
            'customtkinter',
            'Pillow',
            'qrcode',
            'cryptography'
        ]
        
        missing_packages = []
        for package in required_packages:
            try:
                __import__(package)
            except ImportError:
                missing_packages.append(package)
        
        if missing_packages:
            print("缺少以下依赖包:")
            for package in missing_packages:
                print(f"  - {package}")
            print("\n请运行以下命令安装依赖:")
            print("pip install -r requirements.txt")
            return False
        
        return True
    
    def setup_environment():
        """设置运行环境"""
        # 创建必要的目录
        directories = [
            "data",
            "data/logs",
            "assets"
        ]
        
        for directory in directories:
            os.makedirs(directory, exist_ok=True)
        
        # 初始化数据库
        try:
            db.init_database()
            logger.info("数据库初始化完成")
        except Exception as e:
            logger.error(f"数据库初始化失败: {str(e)}")
            return False
        
        return True
    
    def main():
        """主函数"""
        try:
            # 打印启动信息
            app_name = settings.get("app.name", "小红书自动回帖机器人")
            app_version = settings.get("app.version", "1.0.0")
            app_author = settings.get("app.author", "AI Assistant")
            
            print(f"正在启动 {app_name} v{app_version}")
            print(f"作者: {app_author}")
            print("-" * 50)
            
            # 检查依赖
            print("检查依赖包...")
            if not check_dependencies():
                input("按回车键退出...")
                return
            
            # 设置环境
            print("设置运行环境...")
            if not setup_environment():
                print("环境设置失败，程序退出")
                input("按回车键退出...")
                return
            
            # 记录启动日志
            logger.info(f"{app_name} v{app_version} 启动")
            logger.info(f"Python版本: {sys.version}")
            logger.info(f"工作目录: {os.getcwd()}")
            
            print("启动图形界面...")
            
            # 创建并运行主窗口
            app = MainWindow()
            app.run()
            
            logger.info("程序正常退出")
            
        except KeyboardInterrupt:
            print("\n用户中断程序")
            logger.info("用户中断程序")
        
        except Exception as e:
            error_msg = f"程序运行异常: {str(e)}"
            print(error_msg)
            logger.critical(error_msg)
            logger.critical(traceback.format_exc())
            
            # 显示错误对话框
            try:
                import tkinter as tk
                from tkinter import messagebox
                
                root = tk.Tk()
                root.withdraw()  # 隐藏主窗口
                
                messagebox.showerror(
                    "程序错误",
                    f"程序运行时发生错误:\n\n{str(e)}\n\n请查看日志文件获取详细信息。"
                )
                
                root.destroy()
            except:
                pass
            
            input("按回车键退出...")
    
    if __name__ == "__main__":
        main()

except ImportError as e:
    print(f"导入模块失败: {str(e)}")
    print("\n请确保已安装所有依赖包:")
    print("pip install -r requirements.txt")
    input("按回车键退出...")

except Exception as e:
    print(f"程序启动失败: {str(e)}")
    print(traceback.format_exc())
    input("按回车键退出...")
